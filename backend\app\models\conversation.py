from sqlalchemy import Column, Integer, String, Bo<PERSON>an, DateTime, Text, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from ..core.database import Base


class MessageType(enum.Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class MessageContentType(enum.Enum):
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    MULTIMODAL = "multimodal"


class Conversation(Base):
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255))
    
    # 配置
    model_name = Column(String(100))
    system_prompt = Column(Text)
    temperature = Column(String(10), default="0.7")
    max_tokens = Column(Integer, default=2000)
    
    # 状态
    is_active = Column(Boolean, default=True)
    message_count = Column(Integer, default=0)
    
    # 所属用户和应用
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    app_id = Column(Integer, ForeignKey("apps.id"), nullable=False)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_message_at = Column(DateTime(timezone=True))
    
    # 关联关系
    user = relationship("User", back_populates="conversations")
    app = relationship("App", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation", order_by="Message.created_at")
    
    def __repr__(self):
        return f"<Conversation(id={self.id}, title='{self.title}', user_id={self.user_id})>"


class Message(Base):
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text, nullable=False)
    
    # 消息类型
    message_type = Column(Enum(MessageType), nullable=False)
    content_type = Column(Enum(MessageContentType), default=MessageContentType.TEXT)
    
    # 附加数据
    extra_data = Column(Text)  # JSON格式存储额外信息
    attachments = Column(Text)  # JSON格式存储附件信息
    
    # RAG相关
    retrieved_chunks = Column(Text)  # JSON格式存储检索到的知识片段
    similarity_scores = Column(Text)  # JSON格式存储相似度分数
    
    # 模型响应信息
    model_name = Column(String(100))
    prompt_tokens = Column(Integer)
    completion_tokens = Column(Integer)
    total_tokens = Column(Integer)
    response_time = Column(String(20))  # 响应时间（毫秒）
    
    # 所属对话
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关联关系
    conversation = relationship("Conversation", back_populates="messages")
    
    def __repr__(self):
        return f"<Message(id={self.id}, type='{self.message_type}', conversation_id={self.conversation_id})>"
