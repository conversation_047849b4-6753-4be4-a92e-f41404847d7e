from sqlalchemy import Column, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, Text, ForeignKey, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..core.database import Base


class KnowledgeBase(Base):
    __tablename__ = "knowledge_bases"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    
    # 配置
    embedding_model = Column(String(100))
    chunk_size = Column(Integer, default=1000)
    chunk_overlap = Column(Integer, default=200)
    
    # 状态
    is_active = Column(Boolean, default=True)
    total_chunks = Column(Integer, default=0)
    
    # 所属应用
    app_id = Column(Integer, ForeignKey("apps.id"), nullable=False)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    app = relationship("App", back_populates="knowledge_bases")
    documents = relationship("Document", back_populates="knowledge_base")
    chunks = relationship("KnowledgeChunk", back_populates="knowledge_base")
    
    def __repr__(self):
        return f"<KnowledgeBase(id={self.id}, name='{self.name}', app_id={self.app_id})>"


class KnowledgeChunk(Base):
    __tablename__ = "knowledge_chunks"
    
    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text, nullable=False)
    extra_data = Column(Text)  # JSON格式存储元数据
    
    # 向量相关
    vector_id = Column(String(100))  # 向量数据库中的ID
    embedding_hash = Column(String(64))  # 向量哈希值，用于去重
    
    # 相似度分数（查询时使用）
    similarity_score = Column(Float)
    
    # 所属文档和知识库
    document_id = Column(Integer, ForeignKey("documents.id"))
    knowledge_base_id = Column(Integer, ForeignKey("knowledge_bases.id"), nullable=False)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    document = relationship("Document", back_populates="chunks")
    knowledge_base = relationship("KnowledgeBase", back_populates="chunks")
    
    def __repr__(self):
        return f"<KnowledgeChunk(id={self.id}, document_id={self.document_id})>"
