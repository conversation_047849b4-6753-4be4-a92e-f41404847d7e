#!/usr/bin/env python3
"""
路由调试脚本
检查FastAPI应用中注册的所有路由
"""

import sys
import os

# 添加backend目录到Python路径
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

try:
    from app.main import app
    
    print("🔍 检查FastAPI应用中的所有路由:")
    print("=" * 60)
    
    for route in app.routes:
        if hasattr(route, 'path') and hasattr(route, 'methods'):
            methods = ', '.join(route.methods) if route.methods else 'N/A'
            print(f"{methods:<10} {route.path}")
        elif hasattr(route, 'path'):
            print(f"{'MOUNT':<10} {route.path}")
    
    print("\n" + "=" * 60)
    print(f"总路由数量: {len(app.routes)}")
    
    # 检查特定的设置路由
    print("\n🔍 检查设置相关路由:")
    settings_routes = [route for route in app.routes if hasattr(route, 'path') and '/settings' in route.path]
    if settings_routes:
        for route in settings_routes:
            methods = ', '.join(route.methods) if hasattr(route, 'methods') and route.methods else 'N/A'
            print(f"✅ {methods:<10} {route.path}")
    else:
        print("❌ 没有找到设置相关路由")
    
    # 检查知识库路由
    print("\n🔍 检查知识库相关路由:")
    knowledge_routes = [route for route in app.routes if hasattr(route, 'path') and '/knowledge' in route.path]
    if knowledge_routes:
        for route in knowledge_routes:
            methods = ', '.join(route.methods) if hasattr(route, 'methods') and route.methods else 'N/A'
            print(f"✅ {methods:<10} {route.path}")
    else:
        print("❌ 没有找到知识库相关路由")

except Exception as e:
    print(f"❌ 导入应用失败: {e}")
    import traceback
    traceback.print_exc()
