#!/usr/bin/env python3
"""
测试创建应用功能
"""

import requests
import json

def test_create_app():
    """测试创建应用功能"""
    
    print("🔍 测试创建应用功能")
    
    # 1. 先登录获取token
    print("\n步骤1: 登录获取token")
    login_url = "http://localhost:8000/api/auth/login"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        print(f"登录状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            token = result.get('access_token')
            print(f"✅ 登录成功!")
            
            # 2. 创建应用
            print(f"\n步骤2: 创建应用")
            create_app_url = "http://localhost:8000/api/apps"
            
            app_data = {
                "name": "测试应用",
                "description": "这是一个测试应用",
                "system_prompt": "你是一个有用的AI助手",
                "welcome_message": "欢迎使用测试应用！",
                "llm_config": {
                    "model": "gpt-3.5-turbo",
                    "temperature": 0.7,
                    "max_tokens": 1000
                },
                "is_public": False
            }
            
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            create_response = requests.post(create_app_url, json=app_data, headers=headers)
            print(f"创建应用状态码: {create_response.status_code}")
            print(f"创建应用响应: {create_response.text}")
            
            if create_response.status_code == 200:
                app_result = create_response.json()
                print(f"✅ 创建应用成功!")
                print(f"应用信息: {json.dumps(app_result, indent=2, ensure_ascii=False)}")
                
                # 3. 获取应用列表验证
                print(f"\n步骤3: 获取应用列表验证")
                list_response = requests.get(create_app_url, headers=headers)
                print(f"获取应用列表状态码: {list_response.status_code}")
                
                if list_response.status_code == 200:
                    apps = list_response.json()
                    print(f"✅ 获取应用列表成功!")
                    print(f"应用数量: {len(apps)}")
                    for app in apps:
                        print(f"  - {app['name']} (ID: {app['id']})")
                else:
                    print(f"❌ 获取应用列表失败: {list_response.text}")
                    
            else:
                print(f"❌ 创建应用失败")
                try:
                    error_detail = create_response.json()
                    print(f"错误详情: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
                except:
                    print(f"错误响应: {create_response.text}")
            
        else:
            print(f"❌ 登录失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_create_app()
