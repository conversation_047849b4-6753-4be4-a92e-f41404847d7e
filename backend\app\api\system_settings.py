from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime
import json
import os

from ..core.database import get_db
from ..models.user import User
from .auth import get_current_user

router = APIRouter()


class SystemSettings(BaseModel):
    """系统设置模型"""
    # 模型配置
    defaultModel: str = "gpt-3.5-turbo"
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    maxTokens: int = Field(default=2000, ge=1, le=8000)
    enableStreaming: bool = True

    # API配置
    openaiApiKey: Optional[str] = None
    openaiBaseUrl: str = "https://api.openai.com/v1"
    ollamaBaseUrl: str = "http://localhost:11434"
    qwenApiKey: Optional[str] = None

    # 系统配置
    maxFileSize: int = Field(default=50, ge=1, le=500)  # MB
    supportedFileTypes: str = "pdf,doc,docx,txt,md,ppt,pptx,xls,xlsx"
    enableMonitoring: bool = False
    enableLogging: bool = True

    # 向量数据库配置
    vectorStoreType: str = "faiss"  # faiss, chroma, pinecone
    chunkSize: int = Field(default=1000, ge=100, le=5000)
    chunkOverlap: int = Field(default=200, ge=0, le=1000)


# 默认设置
DEFAULT_SETTINGS = {
    "defaultModel": "gpt-3.5-turbo",
    "temperature": 0.7,
    "maxTokens": 2000,
    "enableStreaming": True,
    "openaiApiKey": os.getenv("OPENAI_API_KEY", "sk-VSzTqjHsZAbLMK7gv32TarlTglNaY2yxC4YLd5IFWlCOgXHo"),
    "openaiBaseUrl": os.getenv("OPENAI_BASE_URL", "https://api.agicto.cn/v1"),
    "ollamaBaseUrl": "http://localhost:11434",
    "qwenApiKey": os.getenv("QWEN_API_KEY"),
    "maxFileSize": 50,
    "supportedFileTypes": "pdf,doc,docx,txt,md,ppt,pptx,xls,xlsx",
    "enableMonitoring": False,
    "enableLogging": True,
    "vectorStoreType": "faiss",
    "chunkSize": 1000,
    "chunkOverlap": 200
}

# 设置存储文件路径
SETTINGS_FILE = "settings.json"


def load_settings() -> dict:
    """加载设置"""
    try:
        if os.path.exists(SETTINGS_FILE):
            with open(SETTINGS_FILE, 'r', encoding='utf-8') as f:
                settings = json.load(f)
                # 合并默认设置，确保所有字段都存在
                merged_settings = DEFAULT_SETTINGS.copy()
                merged_settings.update(settings)
                return merged_settings
        else:
            return DEFAULT_SETTINGS.copy()
    except Exception as e:
        print(f"加载设置失败: {e}")
        return DEFAULT_SETTINGS.copy()


def save_settings(settings: dict) -> bool:
    """保存设置"""
    try:
        with open(SETTINGS_FILE, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存设置失败: {e}")
        return False


@router.get("/settings")
async def get_settings(current_user: User = Depends(get_current_user)):
    """获取系统设置"""
    try:
        settings = load_settings()
        return settings
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取设置失败: {str(e)}"
        )


@router.put("/settings")
async def update_settings(
    settings: SystemSettings,
    current_user: User = Depends(get_current_user)
):
    """更新系统设置"""
    try:
        # 加载当前设置
        current_settings = load_settings()
        
        # 更新设置
        settings_dict = settings.dict(exclude_unset=True)
        current_settings.update(settings_dict)
        
        # 保存设置
        if save_settings(current_settings):
            return {
                "message": "设置保存成功",
                "settings": current_settings
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="设置保存失败"
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新设置失败: {str(e)}"
        )


@router.post("/settings/reset")
async def reset_settings(current_user: User = Depends(get_current_user)):
    """重置设置为默认值"""
    try:
        default_settings = DEFAULT_SETTINGS.copy()
        if save_settings(default_settings):
            return {
                "message": "设置已重置为默认值",
                "settings": default_settings
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="重置设置失败"
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重置设置失败: {str(e)}"
        )


@router.get("/settings/models")
async def get_available_models():
    """获取可用的模型列表"""
    return {
        "openai_models": [
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k",
            "gpt-4",
            "gpt-4-turbo",
            "gpt-4o",
            "gpt-4o-mini"
        ],
        "ollama_models": [
            "llama3",
            "llama3:8b",
            "llama3:70b",
            "qwen2",
            "qwen2:7b",
            "chatglm3",
            "baichuan2"
        ],
        "qwen_models": [
            "qwen-turbo",
            "qwen-plus",
            "qwen-max",
            "qwen-max-longcontext"
        ]
    }
