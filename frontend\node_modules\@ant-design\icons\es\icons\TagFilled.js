import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import TagFilledSvg from "@ant-design/icons-svg/es/asn/TagFilled";
import AntdIcon from "../components/AntdIcon";
var TagFilled = function TagFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: TagFilledSvg
  }));
};

/**![tag](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkzOCA0NTguOGwtMjkuNi0zMTIuNmMtMS41LTE2LjItMTQuNC0yOS0zMC42LTMwLjZMNTY1LjIgODZoLS40Yy0zLjIgMC01LjcgMS03LjYgMi45TDg4LjkgNTU3LjJhOS45NiA5Ljk2IDAgMDAwIDE0LjFsMzYzLjggMzYzLjhjMS45IDEuOSA0LjQgMi45IDcuMSAyLjlzNS4yLTEgNy4xLTIuOWw0NjguMy00NjguM2MyLTIuMSAzLTUgMi44LTh6TTY5OSAzODdjLTM1LjMgMC02NC0yOC43LTY0LTY0czI4LjctNjQgNjQtNjQgNjQgMjguNyA2NCA2NC0yOC43IDY0LTY0IDY0eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(TagFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TagFilled';
}
export default RefIcon;