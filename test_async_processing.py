#!/usr/bin/env python3
"""
直接测试异步文档处理功能
"""

import sys
import os
import asyncio

# 添加后端路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.api.knowledge_base import process_document_async
from app.core.database import get_db
from app.models.document import Document

async def test_async_processing():
    """测试异步处理功能"""
    print("🔍 测试异步文档处理功能")
    
    # 获取数据库连接
    db = next(get_db())
    
    try:
        # 查找pending状态的文档
        pending_docs = db.query(Document).filter(Document.status == "pending").all()
        
        if not pending_docs:
            print("❌ 没有找到pending状态的文档")
            return
        
        print(f"✅ 找到 {len(pending_docs)} 个pending状态的文档")
        
        for doc in pending_docs:
            print(f"\n📄 处理文档 ID: {doc.id}")
            print(f"   文件名: {doc.original_filename}")
            print(f"   状态: {doc.status}")
            print(f"   知识库ID: {doc.knowledge_base_id}")
            
            try:
                # 直接调用异步处理函数
                await process_document_async(doc.id)
                
                # 刷新文档状态
                db.refresh(doc)
                print(f"   处理后状态: {doc.status}")
                
                if doc.status == "completed":
                    print(f"   ✅ 文档处理成功")
                    print(f"   总块数: {doc.total_chunks}")
                elif doc.status == "failed":
                    print(f"   ❌ 文档处理失败")
                else:
                    print(f"   ⚠️ 文档状态未改变: {doc.status}")
                    
            except Exception as e:
                print(f"   ❌ 处理文档时出错: {e}")
                import traceback
                traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    try:
        asyncio.run(test_async_processing())
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
