from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
import json

from ..core.database import get_db
from ..models.app import App
from ..models.user import User
from .auth import get_current_user
from ..services.permission_service import permission_service, Permission

router = APIRouter()


class AppCreate(BaseModel):
    name: str
    description: str = None
    avatar_url: str = None
    system_prompt: str = None
    welcome_message: str = None
    llm_config: dict = None
    is_public: bool = False


class AppUpdate(BaseModel):
    name: str = None
    description: str = None
    avatar_url: str = None
    system_prompt: str = None
    welcome_message: str = None
    llm_config: dict = None
    is_public: bool = None
    is_active: bool = None


class AppResponse(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    avatar_url: Optional[str] = None
    system_prompt: Optional[str] = None
    welcome_message: Optional[str] = None
    llm_config: Optional[str] = Field(None, alias="model_config")  # 使用alias避免冲突
    is_active: bool
    is_public: bool
    owner_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        populate_by_name = True  # 允许使用字段名和alias


@router.post("/", response_model=AppResponse)
@router.post("", response_model=AppResponse)  # 添加不带斜杠的路由
async def create_app(
    app_data: AppCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建新应用"""
    # 检查创建应用权限
    permission_service.require_permission(current_user, Permission.APP_CREATE)

    db_app = App(
        name=app_data.name,
        description=app_data.description,
        avatar_url=app_data.avatar_url,
        system_prompt=app_data.system_prompt,
        welcome_message=app_data.welcome_message,
        model_config=json.dumps(app_data.llm_config) if app_data.llm_config else None,
        is_public=app_data.is_public,
        owner_id=current_user.id
    )

    db.add(db_app)
    db.commit()
    db.refresh(db_app)

    return db_app


@router.get("/", response_model=List[AppResponse])
@router.get("", response_model=List[AppResponse])  # 添加不带斜杠的路由
async def get_apps(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取应用列表"""
    # 使用权限服务获取可访问的应用
    apps = permission_service.get_accessible_apps(current_user, db)

    # 应用分页
    total = len(apps)
    apps = apps[skip:skip + limit]

    return apps


@router.get("/{app_id}", response_model=AppResponse)
async def get_app(
    app_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指定应用"""
    app = db.query(App).filter(App.id == app_id).first()
    if not app:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )

    # 检查访问权限
    if not permission_service.can_access_app(current_user, app):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )

    return app


@router.put("/{app_id}", response_model=AppResponse)
async def update_app(
    app_id: int,
    app_update: AppUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新应用"""
    app = db.query(App).filter(App.id == app_id).first()
    if not app:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )

    # 检查更新权限
    permission_service.require_app_permission(current_user, app, Permission.APP_UPDATE)

    # 更新应用信息
    for field, value in app_update.dict(exclude_unset=True).items():
        if field == "llm_config" and value is not None:
            setattr(app, "model_config", json.dumps(value))
        elif field == "model_config" and value is not None:
            setattr(app, field, json.dumps(value))
        else:
            setattr(app, field, value)

    db.commit()
    db.refresh(app)

    return app


@router.delete("/{app_id}")
async def delete_app(
    app_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除应用"""
    app = db.query(App).filter(App.id == app_id).first()
    if not app:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )

    # 检查删除权限
    permission_service.require_app_permission(current_user, app, Permission.APP_DELETE)

    db.delete(app)
    db.commit()

    return {"message": "应用删除成功"}
