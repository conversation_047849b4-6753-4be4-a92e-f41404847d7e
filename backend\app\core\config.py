from pydantic_settings import BaseSettings
from typing import Optional, List
import os


class Settings(BaseSettings):
    # 应用基础配置
    APP_NAME: str = "AIChat"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = True
    LOG_LEVEL: str = "INFO"
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://aichat_user:aichat_password@localhost:5432/aichat"
    REDIS_URL: str = "redis://localhost:6379"
    
    # JWT配置
    JWT_SECRET_KEY: str = "your-secret-key-change-in-production"
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRE_MINUTES: int = 1440  # 24小时
    
    # 大模型配置
    OPENAI_API_KEY: Optional[str] = "sk-VSzTqjHsZAbLMK7gv32TarlTglNaY2yxC4YLd5IFWlCOgXHo"
    OPENAI_BASE_URL: str = "https://api.agicto.cn/v1"
    OPENAI_MODEL: str = "gpt-3.5-turbo"
    
    # Ollama配置
    OLLAMA_BASE_URL: str = "http://localhost:11434"
    OLLAMA_MODEL: str = "llama3"
    
    # 通义千问配置
    QWEN_API_KEY: Optional[str] = None
    QWEN_BASE_URL: str = "https://dashscope.aliyuncs.com/api/v1"
    
    # 文心一言配置
    ERNIE_API_KEY: Optional[str] = None
    ERNIE_SECRET_KEY: Optional[str] = None
    
    # Embedding模型配置
    EMBEDDING_MODEL: str = "text-embedding-ada-002"
    EMBEDDING_DIMENSION: int = 1536
    
    # 向量数据库配置
    VECTOR_STORE_TYPE: str = "faiss"  # faiss, chroma, pinecone
    VECTOR_DIMENSION: int = 1536
    CHUNK_SIZE: int = 1000
    CHUNK_OVERLAP: int = 200
    
    # 文件上传配置
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    UPLOAD_PATH: str = "./uploads"
    VECTOR_STORE_PATH: str = "./vector_store"
    ALLOWED_EXTENSIONS: List[str] = [
        "pdf", "doc", "docx", "ppt", "pptx", 
        "xls", "xlsx", "txt", "md", 
        "jpg", "jpeg", "png", "gif"
    ]
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:3001",
        "http://localhost:8080",
    ]
    
    # 邮件配置
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    SMTP_TLS: bool = True
    
    # 监控配置
    PROMETHEUS_ENABLED: bool = False
    GRAFANA_ENABLED: bool = False
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局设置实例
settings = Settings()

# 确保必要的目录存在
os.makedirs(settings.UPLOAD_PATH, exist_ok=True)
os.makedirs(settings.VECTOR_STORE_PATH, exist_ok=True)
os.makedirs("logs", exist_ok=True)
