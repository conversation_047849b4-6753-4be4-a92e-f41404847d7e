#!/usr/bin/env python3
"""
直接测试文档处理功能
"""

import sys
import os
import asyncio
import requests

# 添加后端路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.api.knowledge_base import process_document_async

BASE_URL = "http://localhost:8000"

async def test_direct_processing():
    """直接测试文档处理"""
    print("🔧 直接测试文档处理功能")
    
    # 1. 登录获取token
    print("\n1. 登录...")
    login_response = requests.post(f"{BASE_URL}/api/auth/login", json={
        'username': 'admin',
        'password': 'admin123'
    })
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.text}")
        return
    
    token = login_response.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    print("✅ 登录成功")
    
    # 2. 上传测试文档
    print("\n2. 上传测试文档...")
    text_data = {
        'filename': '直接处理测试文档.txt',
        'content': '''这是一个用于直接处理测试的文档。

人工智能（AI）是一门研究如何让计算机模拟人类智能的科学。它包括以下几个主要领域：

1. 机器学习（Machine Learning）
   - 监督学习：使用标记数据训练模型
   - 无监督学习：从未标记数据中发现模式
   - 强化学习：通过试错学习最优策略

2. 深度学习（Deep Learning）
   - 神经网络：模拟人脑神经元的计算模型
   - 卷积神经网络（CNN）：主要用于图像处理
   - 循环神经网络（RNN）：主要用于序列数据处理

3. 自然语言处理（NLP）
   - 文本分析：理解和处理人类语言
   - 语言生成：自动生成人类可读的文本
   - 机器翻译：在不同语言之间进行翻译

4. 计算机视觉（Computer Vision）
   - 图像识别：识别图像中的对象和场景
   - 目标检测：定位图像中的特定对象
   - 图像分割：将图像分割成不同的区域

这些技术正在改变我们的生活方式，从智能手机到自动驾驶汽车，AI技术无处不在。''',
        'knowledge_base_id': 8
    }
    
    upload_response = requests.post(f"{BASE_URL}/api/knowledge/8/documents/text", 
                                   json=text_data, headers=headers)
    
    if upload_response.status_code != 200:
        print(f"❌ 文档上传失败: {upload_response.text}")
        return
    
    doc_data = upload_response.json()
    doc_id = doc_data['id']
    print(f"✅ 文档上传成功，ID: {doc_id}")
    print(f"   初始状态: {doc_data['status']}")
    
    # 3. 直接调用异步处理函数
    print(f"\n3. 直接处理文档 ID: {doc_id}...")
    try:
        await process_document_async(doc_id)
        print("✅ 异步处理函数调用完成")
    except Exception as e:
        print(f"❌ 异步处理失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 4. 检查处理结果
    print("\n4. 检查处理结果...")
    docs_response = requests.get(f"{BASE_URL}/api/knowledge/8/documents", headers=headers)
    if docs_response.status_code == 200:
        docs = docs_response.json()
        doc = next((d for d in docs if d['id'] == doc_id), None)
        if doc:
            print(f"   文档状态: {doc['status']}")
            if doc['status'] == 'completed':
                print(f"   ✅ 文档处理成功")
                print(f"   总块数: {doc['total_chunks']}")
                
                # 5. 测试RAG检索
                print("\n5. 测试RAG检索...")
                chat_data = {
                    'message': '什么是机器学习？请详细解释。',
                    'app_id': '5'
                }
                
                chat_response = requests.post(f"{BASE_URL}/api/conversations/chat", 
                                             json=chat_data, headers=headers)
                
                if chat_response.status_code == 200:
                    result = chat_response.json()
                    print(f"   ✅ RAG启用: {result.get('rag_enabled', False)}")
                    print(f"   📚 使用知识库: {result.get('knowledge_bases_used', [])}")
                    print(f"   📄 检索片段数: {len(result.get('retrieved_chunks', []))}")
                    print(f"   🤖 回答: {result['response'][:200]}...")
                    
                    if result.get('rag_enabled') and result.get('retrieved_chunks'):
                        print("   ✅ RAG功能正常工作")
                        
                        # 显示检索到的片段
                        chunks = result.get('retrieved_chunks', [])
                        if chunks:
                            print(f"\n   📄 检索到的知识片段:")
                            for i, chunk in enumerate(chunks[:2], 1):
                                print(f"      片段 {i}: {chunk.get('content', '')[:100]}...")
                    else:
                        print("   ⚠️ RAG功能可能未正常工作")
                else:
                    print(f"   ❌ 对话失败: {chat_response.text}")
                
                # 6. 测试文档删除
                print(f"\n6. 测试文档删除 (ID: {doc_id})...")
                delete_response = requests.delete(f"{BASE_URL}/api/knowledge/8/documents/{doc_id}", 
                                                 headers=headers)
                
                if delete_response.status_code == 200:
                    print("   ✅ 文档删除成功")
                else:
                    print(f"   ❌ 文档删除失败: {delete_response.status_code} - {delete_response.text}")
                
            elif doc['status'] == 'failed':
                print(f"   ❌ 文档处理失败: {doc.get('error_message', '未知错误')}")
            else:
                print(f"   ⚠️ 文档状态未改变: {doc['status']}")
        else:
            print("   ❌ 找不到文档")
    else:
        print(f"   ❌ 获取文档列表失败: {docs_response.text}")

if __name__ == "__main__":
    try:
        asyncio.run(test_direct_processing())
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
