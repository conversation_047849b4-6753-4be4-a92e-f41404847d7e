#!/usr/bin/env python3
"""
修复文件管理问题的脚本
"""

import sys
import os
import requests
import json

# 添加后端路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.database import get_db
from app.models.document import Document

BASE_URL = "http://localhost:8000"

def fix_file_management_issues():
    """修复文件管理问题"""
    print("🔧 开始修复文件管理问题")
    
    # 1. 登录获取token
    print("\n1. 登录获取token...")
    login_response = requests.post(f"{BASE_URL}/api/auth/login", json={
        'username': 'admin',
        'password': 'admin123'
    })
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.text}")
        return
    
    token = login_response.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    print("✅ 登录成功")
    
    # 2. 检查并清理失败的文档
    print("\n2. 检查数据库中的文档状态...")
    db = next(get_db())
    
    try:
        # 查找所有文档
        all_docs = db.query(Document).all()
        print(f"   总文档数: {len(all_docs)}")
        
        pending_docs = [doc for doc in all_docs if doc.status == "pending"]
        failed_docs = [doc for doc in all_docs if doc.status == "failed"]
        completed_docs = [doc for doc in all_docs if doc.status == "completed"]
        
        print(f"   Pending: {len(pending_docs)}")
        print(f"   Failed: {len(failed_docs)}")
        print(f"   Completed: {len(completed_docs)}")
        
        # 删除失败的文档
        if failed_docs:
            print(f"\n3. 清理 {len(failed_docs)} 个失败的文档...")
            for doc in failed_docs:
                print(f"   删除文档: {doc.original_filename} (ID: {doc.id})")
                
                # 删除文件（如果存在）
                if doc.file_path and os.path.exists(doc.file_path):
                    try:
                        os.remove(doc.file_path)
                        print(f"     ✅ 删除文件: {doc.file_path}")
                    except Exception as e:
                        print(f"     ⚠️ 删除文件失败: {e}")
                
                # 从数据库删除
                db.delete(doc)
            
            db.commit()
            print("   ✅ 失败文档清理完成")
        
        # 重置pending文档状态
        if pending_docs:
            print(f"\n4. 重置 {len(pending_docs)} 个pending文档状态...")
            for doc in pending_docs:
                print(f"   重置文档: {doc.original_filename} (ID: {doc.id})")
                doc.status = "pending"
            
            db.commit()
            print("   ✅ Pending文档状态重置完成")
        
    finally:
        db.close()
    
    # 3. 测试新的文档上传
    print("\n5. 测试新的文档上传...")
    
    # 获取知识库
    kb_response = requests.get(f"{BASE_URL}/api/knowledge/", headers=headers)
    if kb_response.status_code != 200:
        print(f"❌ 获取知识库失败: {kb_response.text}")
        return
    
    knowledge_bases = kb_response.json()
    if not knowledge_bases:
        print("❌ 没有找到知识库")
        return
    
    kb_id = knowledge_bases[0]['id']
    print(f"   使用知识库 ID: {kb_id}")
    
    # 上传测试文档
    text_data = {
        'filename': '修复测试文档.txt',
        'content': '''这是一个修复测试文档。

本文档用于测试文件管理功能的修复情况：

1. 文档上传功能
2. 向量化处理功能  
3. RAG检索功能
4. 文档删除功能

如果您看到这个文档被成功处理，说明修复工作已经完成。''',
        'knowledge_base_id': kb_id
    }
    
    upload_response = requests.post(f"{BASE_URL}/api/knowledge/{kb_id}/documents/text", 
                                   json=text_data, headers=headers)
    
    if upload_response.status_code == 200:
        doc_data = upload_response.json()
        doc_id = doc_data['id']
        print(f"   ✅ 测试文档上传成功，ID: {doc_id}")
        print(f"   状态: {doc_data['status']}")
        
        # 等待处理完成
        print("\n6. 等待文档处理完成...")
        import time
        max_wait = 30
        wait_time = 0
        
        while wait_time < max_wait:
            docs_response = requests.get(f"{BASE_URL}/api/knowledge/{kb_id}/documents", headers=headers)
            if docs_response.status_code == 200:
                docs = docs_response.json()
                doc = next((d for d in docs if d['id'] == doc_id), None)
                if doc:
                    print(f"   状态: {doc['status']} (等待时间: {wait_time}s)")
                    if doc['status'] == 'completed':
                        print("   ✅ 文档处理完成")
                        print(f"   总块数: {doc['total_chunks']}")
                        break
                    elif doc['status'] == 'failed':
                        print(f"   ❌ 文档处理失败")
                        break
            
            time.sleep(3)
            wait_time += 3
        
        if wait_time >= max_wait:
            print("   ⚠️ 文档处理超时")
        
        # 测试删除
        print(f"\n7. 测试文档删除 (ID: {doc_id})...")
        delete_response = requests.delete(f"{BASE_URL}/api/knowledge/{kb_id}/documents/{doc_id}", 
                                         headers=headers)
        
        if delete_response.status_code == 200:
            print("   ✅ 文档删除成功")
        else:
            print(f"   ❌ 文档删除失败: {delete_response.status_code} - {delete_response.text}")
    
    else:
        print(f"   ❌ 测试文档上传失败: {upload_response.text}")
    
    print("\n🎉 文件管理问题修复完成！")

if __name__ == "__main__":
    try:
        fix_file_management_issues()
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
