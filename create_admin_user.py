#!/usr/bin/env python3
"""
创建默认管理员用户
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from sqlalchemy.orm import Session
from backend.app.core.database import SessionLocal, engine
from backend.app.core.security import get_password_hash
from backend.app.models.user import User
from backend.app.core.database import Base

def create_admin_user():
    """创建默认管理员用户"""
    
    # 创建所有表
    print("🔧 创建数据库表...")
    Base.metadata.create_all(bind=engine)
    
    # 创建数据库会话
    db: Session = SessionLocal()
    
    try:
        # 检查admin用户是否已存在
        existing_user = db.query(User).filter(User.username == "admin").first()
        
        if existing_user:
            print("✅ 管理员用户已存在")
            print(f"   用户名: {existing_user.username}")
            print(f"   邮箱: {existing_user.email}")
            print(f"   是否激活: {existing_user.is_active}")
            print(f"   是否超级用户: {existing_user.is_superuser}")
            return
        
        # 创建管理员用户
        print("🔧 创建默认管理员用户...")
        
        hashed_password = get_password_hash("admin123")
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=hashed_password,
            full_name="系统管理员",
            is_active=True,
            is_superuser=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print("✅ 默认管理员用户创建成功!")
        print("   用户名: admin")
        print("   密码: admin123")
        print("   邮箱: <EMAIL>")
        
    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        db.rollback()
        
    finally:
        db.close()

if __name__ == "__main__":
    create_admin_user()
