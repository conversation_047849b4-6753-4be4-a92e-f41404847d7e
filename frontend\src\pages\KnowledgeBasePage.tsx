import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON><PERSON>, <PERSON>ton, Card, Table, Modal, Form, Input, Select,
  message, Spin, Tag, Space, Divider
} from 'antd'
import {
  PlusOutlined, DatabaseOutlined, FileTextOutlined, UploadOutlined,
  DeleteOutlined, EyeOutlined
} from '@ant-design/icons'
import { useAppStore } from '../stores/appStore'
import { knowledgeService, KnowledgeBase, KnowledgeBaseCreateRequest } from '../services/knowledgeService'
import { appService } from '../services/appService'
import DocumentManager from '../components/DocumentManager'

const { Title } = Typography
const { TextArea } = Input
const { Option } = Select

const KnowledgeBasePage: React.FC = () => {
  const { apps, setApps } = useAppStore()
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([])
  const [loading, setLoading] = useState(false)
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [documentManagerVisible, setDocumentManagerVisible] = useState(false)
  const [selectedKB, setSelectedKB] = useState<KnowledgeBase | null>(null)
  const [form] = Form.useForm()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [appsData, kbData] = await Promise.all([
        appService.getApps(),
        knowledgeService.getKnowledgeBases()
      ])
      setApps(appsData)
      setKnowledgeBases(kbData)
    } catch (error) {
      console.error('加载数据失败:', error)
      message.error('加载数据失败')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateKB = async (values: KnowledgeBaseCreateRequest) => {
    try {
      const newKB = await knowledgeService.createKnowledgeBase(values)
      setKnowledgeBases([...knowledgeBases, newKB])
      message.success('知识库创建成功')
      setCreateModalVisible(false)
      form.resetFields()
    } catch (error) {
      console.error('创建知识库失败:', error)
      message.error('创建知识库失败')
    }
  }

  const handleDeleteKB = async (kbId: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个知识库吗？此操作不可恢复。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await knowledgeService.deleteKnowledgeBase(kbId)
          setKnowledgeBases(knowledgeBases.filter(kb => kb.id !== kbId))
          message.success('知识库删除成功')
        } catch (error) {
          console.error('删除知识库失败:', error)
          message.error('删除知识库失败')
        }
      },
    })
  }

  const handleOpenDocumentManager = (kb: KnowledgeBase) => {
    setSelectedKB(kb)
    setDocumentManagerVisible(true)
  }

  const columns = [
    {
      title: '知识库名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: KnowledgeBase) => (
        <Space>
          <DatabaseOutlined />
          <span>{text}</span>
          <Tag color={record.is_active ? 'green' : 'red'}>
            {record.is_active ? '活跃' : '停用'}
          </Tag>
        </Space>
      ),
    },
    {
      title: '所属应用',
      dataIndex: 'app_id',
      key: 'app_id',
      render: (appId: number) => {
        const app = apps.find(a => a.id === appId)
        return app ? app.name : '未知应用'
      },
    },
    {
      title: '文档数量',
      dataIndex: 'documents',
      key: 'documents',
      render: (documents: any[]) => (
        <Space>
          <FileTextOutlined />
          <span>{documents?.length || 0} 个文档</span>
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: KnowledgeBase) => (
        <Space>
          <Button
            type="link"
            icon={<UploadOutlined />}
            onClick={() => handleOpenDocumentManager(record)}
          >
            文档管理
          </Button>
          <Button 
            type="link" 
            icon={<EyeOutlined />}
            onClick={() => {
              Modal.info({
                title: '知识库详情',
                content: (
                  <div>
                    <p><strong>名称:</strong> {record.name}</p>
                    <p><strong>描述:</strong> {record.description || '无'}</p>
                    <p><strong>嵌入模型:</strong> {record.embedding_model}</p>
                    <p><strong>分块大小:</strong> {record.chunk_size}</p>
                    <p><strong>重叠大小:</strong> {record.chunk_overlap}</p>
                    <Divider />
                    <p><strong>文档列表:</strong></p>
                    {record.documents?.map(doc => (
                      <div key={doc.id}>• {doc.name}</div>
                    )) || '暂无文档'}
                  </div>
                ),
                width: 600,
              })
            }}
          >
            查看详情
          </Button>
          <Button 
            type="link" 
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteKB(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>知识库管理</Title>
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={() => setCreateModalVisible(true)}
        >
          创建知识库
        </Button>
      </div>

      <Card>
        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={knowledgeBases}
            rowKey="id"
            pagination={{ pageSize: 10 }}
          />
        </Spin>
      </Card>

      {/* 创建知识库模态框 */}
      <Modal
        title="创建知识库"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false)
          form.resetFields()
        }}
        onOk={() => form.submit()}
        okText="创建"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateKB}
        >
          <Form.Item
            name="name"
            label="知识库名称"
            rules={[{ required: true, message: '请输入知识库名称' }]}
          >
            <Input placeholder="请输入知识库名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={3} placeholder="请输入知识库描述" />
          </Form.Item>

          <Form.Item
            name="app_id"
            label="关联应用"
            rules={[{ required: true, message: '请选择关联应用' }]}
          >
            <Select placeholder="请选择应用">
              {apps.map(app => (
                <Option key={app.id} value={app.id}>
                  {app.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="embedding_model"
            label="嵌入模型"
            initialValue="text-embedding-ada-002"
          >
            <Select>
              <Option value="text-embedding-ada-002">text-embedding-ada-002</Option>
              <Option value="text-embedding-3-small">text-embedding-3-small</Option>
              <Option value="text-embedding-3-large">text-embedding-3-large</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 文档管理器 */}
      <DocumentManager
        visible={documentManagerVisible}
        onClose={() => {
          setDocumentManagerVisible(false)
          setSelectedKB(null)
          loadData() // 重新加载数据以更新文档数量
        }}
        knowledgeBaseId={selectedKB?.id || 0}
        knowledgeBaseName={selectedKB?.name || ''}
      />
    </div>
  )
}

export default KnowledgeBasePage
