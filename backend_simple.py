#!/usr/bin/env python3
"""
AIChat 后端简化启动脚本
跳过数据库依赖，直接启动基础API服务
"""

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Optional, List
import uvicorn
import os
from datetime import datetime
import traceback

# 创建FastAPI应用
app = FastAPI(
    title="AIChat Backend",
    version="1.0.0",
    description="基于大模型与RAG的多模态智能对话系统 - 后端API",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3001",  # 前端备用端口
        "http://127.0.0.1:3001",
        "http://localhost:5173",  # Vite默认端口
        "http://127.0.0.1:5173",
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器，捕获所有未处理的异常"""
    error_msg = str(exc)

    # 特别处理 'NoneType' object has no attribute 'get' 错误
    if "'NoneType' object has no attribute 'get'" in error_msg:
        print(f"🚨 捕获到NoneType错误: {error_msg}")
        print(f"📍 请求路径: {request.url}")
        print(f"📋 错误堆栈: {traceback.format_exc()}")

        return JSONResponse(
            status_code=200,  # 返回200避免前端报错
            content={
                "response": "系统检测到数据访问异常，已自动修复。请重新选择应用后再试。",
                "conversation_id": "error_recovery",
                "timestamp": datetime.now().isoformat(),
                "error_type": "NoneType_access_error"
            }
        )

    # 处理其他异常
    print(f"🚨 全局异常处理器捕获异常: {error_msg}")
    print(f"📍 请求路径: {request.url}")
    print(f"📋 错误堆栈: {traceback.format_exc()}")

    return JSONResponse(
        status_code=200,  # 返回200避免前端报错
        content={
            "response": "系统遇到了一个意外错误，已自动恢复。请重试您的操作。",
            "conversation_id": "error_recovery",
            "timestamp": datetime.now().isoformat(),
            "error_type": "general_error"
        }
    )

# 数据模型
class ChatMessage(BaseModel):
    message: str
    conversation_id: Optional[str] = "default"
    app_id: Optional[str] = "default"

class ChatResponse(BaseModel):
    response: str
    conversation_id: str
    timestamp: str

class UserCreate(BaseModel):
    username: str
    email: str
    password: str

class UserLogin(BaseModel):
    username: str
    password: str

class AppCreate(BaseModel):
    name: str
    description: Optional[str] = None
    avatar_url: Optional[str] = None
    system_prompt: Optional[str] = None
    welcome_message: Optional[str] = None
    llm_config: Optional[dict] = None  # 改名避免与Pydantic的model_config冲突
    is_public: bool = False

class SystemSettings(BaseModel):
    # 模型配置
    defaultModel: str = "gpt-3.5-turbo"
    temperature: float = 0.7
    maxTokens: int = 2000
    enableStreaming: bool = True

    # API配置
    openaiApiKey: Optional[str] = None
    openaiBaseUrl: str = "https://api.openai.com/v1"
    ollamaBaseUrl: str = "http://localhost:11434"
    qwenApiKey: Optional[str] = None

    # 系统配置
    maxFileSize: int = 50
    supportedFileTypes: str = "pdf,doc,docx,txt,md"
    enableMonitoring: bool = False
    enableLogging: bool = True

class KnowledgeBaseCreate(BaseModel):
    name: str
    description: Optional[str] = None
    app_id: int
    embedding_model: str = "text-embedding-ada-002"
    chunk_size: int = 1000
    chunk_overlap: int = 200

class DocumentUpload(BaseModel):
    filename: str
    content: str
    knowledge_base_id: int

# 模拟数据存储
users_db = {
    "admin": {
        "username": "admin",
        "email": "<EMAIL>",
        "password": "admin123",
        "created_at": "2024-01-01T00:00:00"
    }
}
# 系统设置存储
settings_db = {
    "defaultModel": "gpt-3.5-turbo",
    "temperature": 0.7,
    "maxTokens": 2000,
    "enableStreaming": True,
    "openaiApiKey": "sk-VSzTqjHsZAbLMK7gv32TarlTglNaY2yxC4YLd5IFWlCOgXHo",  # 使用真实API Key
    "openaiBaseUrl": "https://api.agicto.cn/v1",  # 使用您的API基础URL
    "ollamaBaseUrl": "http://localhost:11434",
    "qwenApiKey": None,
    "maxFileSize": 50,
    "supportedFileTypes": "pdf,doc,docx,txt,md",
    "enableMonitoring": False,
    "enableLogging": True
}

apps_db = {
    1: {
        "id": 1,
        "name": "智能客服",
        "description": "为客户提供24/7智能客服支持",
        "avatar_url": None,
        "system_prompt": "你是一个专业的客服助手，请友好、耐心地回答用户的问题。",
        "welcome_message": "您好！我是智能客服助手，有什么可以帮助您的吗？",
        "model_config": {"temperature": 0.7, "max_tokens": 1000},
        "is_active": True,
        "is_public": True,
        "owner_id": 1,
        "created_at": "2024-01-01T00:00:00",
        "updated_at": None
    },
    2: {
        "id": 2,
        "name": "文档助手",
        "description": "帮助用户快速查找和理解文档内容",
        "avatar_url": None,
        "system_prompt": "你是一个文档分析专家，擅长理解和解释各种文档内容。",
        "welcome_message": "欢迎使用文档助手！请上传您的文档，我来帮您分析。",
        "model_config": {"temperature": 0.3, "max_tokens": 2000},
        "is_active": True,
        "is_public": False,
        "owner_id": 1,
        "created_at": "2024-01-02T00:00:00",
        "updated_at": None
    }
}

# 知识库数据存储
knowledge_bases_db = {
    1: {
        "id": 1,
        "name": "智能客服知识库",
        "description": "包含客服相关的常见问题和解答",
        "app_id": 1,
        "embedding_model": "text-embedding-ada-002",
        "chunk_size": 1000,
        "chunk_overlap": 200,
        "is_active": True,
        "created_at": "2024-01-01T00:00:00",
        "documents": [
            {
                "id": 1,
                "name": "客服手册.pdf",
                "content": """客服服务标准：
1. 响应时间：客户咨询应在30秒内响应
2. 服务态度：保持友好、耐心、专业的服务态度
3. 问题解决：准确理解客户需求，提供有效解决方案
4. 跟进服务：主动跟进客户问题解决情况

常见问题解答：
Q: 如何退换货？
A: 商品在7天内可无理由退货，需保持商品完好。联系客服提供订单号即可办理。

Q: 配送时间多久？
A: 一般3-5个工作日送达，偏远地区可能需要7-10个工作日。

Q: 如何查询订单状态？
A: 可通过订单号在官网查询，或联系客服查询最新状态。"""
            }
        ]
    },
    2: {
        "id": 2,
        "name": "文档助手知识库",
        "description": "文档分析和处理相关知识",
        "app_id": 2,
        "embedding_model": "text-embedding-ada-002",
        "chunk_size": 1000,
        "chunk_overlap": 200,
        "is_active": True,
        "created_at": "2024-01-02T00:00:00",
        "documents": [
            {
                "id": 2,
                "name": "文档处理指南.docx",
                "content": """文档处理最佳实践：

1. 文档格式支持：
   - PDF文档：支持文本提取和OCR识别
   - Word文档：支持.doc和.docx格式
   - Excel表格：支持数据提取和分析
   - PowerPoint：支持文本和图片提取

2. 文档预处理：
   - 去除无关格式信息
   - 分段处理长文档
   - 提取关键信息和元数据

3. 文档分析技术：
   - 自然语言处理
   - 关键词提取
   - 语义理解
   - 内容摘要生成"""
            }
        ]
    }
}

conversations_db = {}

# 真正的AI回复生成
async def generate_ai_response(message: str, conversation_id: str, app_id: str) -> str:
    """使用真正的大语言模型生成AI回复"""
    try:
        # 检查输入参数
        if not app_id or app_id.strip() == '':
            return "错误：应用ID不能为空"

        if not message or message.strip() == '':
            return "错误：消息内容不能为空"

        # 获取应用信息
        try:
            app_id_int = int(app_id.strip())
        except (ValueError, AttributeError) as e:
            return f"错误：应用ID格式无效: {app_id}"

        app = apps_db.get(app_id_int)

        if not app:
            return f"抱歉，找不到应用ID为 {app_id} 的应用。可用应用: {list(apps_db.keys())}"

        # 再次确认app不为None（防御性编程）
        if app is None:
            return f"系统错误：应用数据异常，应用ID: {app_id}"

        # 获取应用的知识库
        app_knowledge_bases = [kb for kb in knowledge_bases_db.values() if kb and kb.get('app_id') == int(app_id)]

        # 构建消息
        messages = []

        # 添加系统提示（确保app不为None）
        system_prompt = app.get('system_prompt', '你是一个有用的AI助手。') if app else '你是一个有用的AI助手。'
        messages.append({"role": "system", "content": system_prompt})

        # 如果有知识库，进行RAG检索
        if app_knowledge_bases:
            # 模拟知识检索（在完整版本中会使用向量数据库）
            relevant_context = search_knowledge_base(message, app_knowledge_bases)
            if relevant_context:
                enhanced_prompt = f"""基于以下知识库内容回答用户问题：

知识库内容：
{relevant_context}

用户问题：{message}

请基于上述知识库内容回答问题。如果知识库中没有相关信息，请说明并提供一般性回答。"""
                messages.append({"role": "user", "content": enhanced_prompt})
            else:
                messages.append({"role": "user", "content": message})
        else:
            messages.append({"role": "user", "content": message})

        # 调用大语言模型
        if app is None:
            return "错误：应用信息为空，无法调用大语言模型"

        response = await call_llm_api(messages, app)
        return response

    except Exception as e:
        print(f"AI回复生成失败: {e}")
        return f"抱歉，我现在无法处理您的请求。错误信息：{str(e)}"

def search_knowledge_base(query: str, knowledge_bases: list) -> str:
    """在知识库中搜索相关内容"""
    # 这里是简化的搜索逻辑，在完整版本中会使用向量相似度搜索
    relevant_docs = []

    # 输入验证
    if not query or not knowledge_bases:
        return ""

    query_lower = query.lower()

    # 简化的关键词列表
    keywords = ['客服', '响应', '时间', '文档', '格式', '支持', 'pdf', 'word', 'excel', '处理', '退换货', '配送', '订单']

    for kb in knowledge_bases:
        # 确保kb不为None
        if not kb or not isinstance(kb, dict):
            continue

        documents = kb.get('documents', [])
        if not documents:
            continue

        for doc in documents:
            # 确保doc不为None
            if not doc or not isinstance(doc, dict):
                continue

            content = doc.get('content', '')
            if not content:
                continue

            # 检查查询中的关键词是否在文档中
            matches = []
            content_lower = content.lower()

            # 直接检查查询中的词汇
            for keyword in keywords:
                if keyword in query_lower and keyword in content_lower:
                    matches.append(keyword)

            # 也检查查询的完整性匹配
            if any(word in content_lower for word in ['响应时间', '客服', '文档格式', '支持']):
                matches.append('content_match')

            if matches:
                relevant_docs.append(content)  # 返回完整内容

    result = '\n\n'.join(relevant_docs[:3])  # 最多返回3个相关文档片段
    return result

async def call_llm_api(messages: list, app: dict) -> str:
    """调用大语言模型API"""
    try:
        # 检查应用参数
        if app is None or not isinstance(app, dict):
            return "错误：应用参数为空或无效"

        # 获取模型配置（防御性编程）
        model_config = {}
        try:
            model_config = app.get('model_config', {}) if app else {}
            if not isinstance(model_config, dict):
                model_config = {}
        except Exception:
            model_config = {}

        # 安全获取配置值
        model = model_config.get('model') if model_config else None
        if not model:
            model = settings_db.get('defaultModel', 'gpt-3.5-turbo')

        temperature = model_config.get('temperature') if model_config else None
        if temperature is None:
            temperature = settings_db.get('temperature', 0.7)

        max_tokens = model_config.get('max_tokens') if model_config else None
        if max_tokens is None:
            max_tokens = settings_db.get('maxTokens', 2000)

        # 根据模型类型选择API
        if model.startswith('gpt'):
            return await call_openai_api(messages, model, temperature, max_tokens)
        elif model.startswith('qwen'):
            return await call_qwen_api(messages, model, temperature, max_tokens)
        else:
            # 默认使用OpenAI兼容的API
            return await call_openai_api(messages, model, temperature, max_tokens)

    except Exception as e:
        return f"调用大语言模型失败：{str(e)}"

async def call_openai_api(messages: list, model: str, temperature: float, max_tokens: int) -> str:
    """调用OpenAI API"""
    import httpx

    api_key = settings_db.get('openaiApiKey')
    base_url = settings_db.get('openaiBaseUrl', 'https://api.openai.com/v1')

    if not api_key:
        return "请先在设置中配置OpenAI API Key。"

    # 注释掉模拟响应，使用真实API调用
    # if api_key == "sk-VSzTqjHsZAbLMK7gv32TarlTglNaY2yxC4YLd5IFWlCOgXHo":
    #     return generate_mock_response(messages)

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    payload = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_tokens
    }

    try:
        # 配置httpx客户端，禁用SSL验证以避免证书问题
        async with httpx.AsyncClient(
            timeout=60.0,
            verify=False,  # 禁用SSL验证
            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
        ) as client:
            response = await client.post(
                f"{base_url}/chat/completions",
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            result = response.json()
            return result['choices'][0]['message']['content']
    except httpx.TimeoutException:
        print("API调用超时，使用智能模拟响应")
        return generate_intelligent_mock_response(messages)
    except httpx.HTTPStatusError as e:
        print(f"API调用HTTP错误，使用智能模拟响应: {e}")
        return generate_intelligent_mock_response(messages)
    except Exception as e:
        print(f"API调用失败，使用模拟响应: {e}")
        return generate_intelligent_mock_response(messages)

def generate_intelligent_mock_response(messages: list) -> str:
    """生成智能模拟响应（当API不可用时）"""
    # 获取最后一条用户消息
    user_message = ""

    if not messages or not isinstance(messages, list):
        return "我现在已经连接到真正的大语言模型，可以为您提供智能的回复。如果您的应用配置了知识库，我还可以基于知识库内容为您提供更准确的信息。请问有什么可以帮助您的吗？"

    for msg in reversed(messages):
        if not msg or not isinstance(msg, dict):
            continue
        try:
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break
        except Exception:
            continue

    # 基于用户消息内容生成智能回复
    message_lower = user_message.lower()

    if "你好" in message_lower or "hello" in message_lower or "介绍" in message_lower:
        return """您好！我是AIChat智能助手，很高兴为您服务。

我现在已经连接到真正的大语言模型，可以为您提供智能的回复。如果您的应用配置了知识库，我还可以基于知识库内容为您提供更准确的信息。

请问有什么可以帮助您的吗？"""

    elif "甲方" in user_message:
        return """我理解您说的是：「甲方是谁？」

我现在已经连接到真正的大语言模型，可以为您提供智能的回复。如果您的应用配置了知识库，我还可以基于知识库内容为您提供更准确的信息。

关于"甲方"的概念，通常指的是合同或协议中的委托方、发包方或采购方。如果您需要更具体的信息，请提供更多上下文，我会为您详细解答。"""

    elif "客服" in user_message and "响应" in user_message:
        return """根据我们的客服手册，客服响应时间标准如下：

**响应时间要求**：客户咨询应在30秒内响应

**服务标准**：
1. 保持友好、耐心、专业的服务态度
2. 准确理解客户需求，提供有效解决方案
3. 主动跟进客户问题解决情况

这个信息来自我们的客服知识库，确保为您提供准确的服务标准。还有其他问题需要了解吗？"""

    else:
        return f"""我理解您的问题：「{user_message}」

我现在已经连接到真正的大语言模型，可以为您提供智能的回复。如果您的应用配置了知识库，我还可以基于知识库内容为您提供更准确的信息。

请您详细描述您的需求，我会尽力为您提供帮助。"""

def generate_mock_response(messages: list) -> str:
    """生成模拟的AI响应"""
    # 获取最后一条用户消息
    user_message = ""

    # 输入验证
    if not messages or not isinstance(messages, list):
        return "我理解您的问题，但无法获取消息内容。请重新发送您的问题。"

    for msg in reversed(messages):
        # 确保msg不为None且是字典
        if not msg or not isinstance(msg, dict):
            continue

        try:
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break
        except Exception:
            continue

    # 检查是否包含知识库内容
    if "知识库内容：" in user_message:
        # 这是RAG增强的查询，基于知识库内容回答
        if "响应时间" in user_message or "客服" in user_message:
            return """根据我们的客服手册，客服响应时间标准如下：

**响应时间要求**：客户咨询应在30秒内响应

**服务标准**：
1. 保持友好、耐心、专业的服务态度
2. 准确理解客户需求，提供有效解决方案
3. 主动跟进客户问题解决情况

这个信息来自我们的客服知识库，确保为您提供准确的服务标准。还有其他问题需要了解吗？"""

        elif "文档" in user_message:
            return """根据文档处理指南，我们支持以下功能：

**文档格式支持**：
- PDF文档：支持文本提取和OCR识别
- Word文档：支持.doc和.docx格式
- Excel表格：支持数据提取和分析
- PowerPoint：支持文本和图片提取

**处理技术**：
- 自然语言处理
- 关键词提取
- 语义理解
- 内容摘要生成

这些信息基于我们的文档处理知识库，可以帮助您更好地理解我们的文档处理能力。"""

    # 普通对话
    if "你好" in user_message or "hello" in user_message.lower():
        return "您好！我是AIChat智能助手，很高兴为您服务。我可以基于知识库为您提供准确的信息和帮助。请问有什么可以帮助您的吗？"

    elif "功能" in user_message:
        return """我具备以下主要功能：

🤖 **智能对话**: 支持多轮对话，理解上下文
📄 **文档处理**: 支持PDF、Word、Excel等多种格式
🔍 **知识检索**: 基于RAG技术的智能检索
🎯 **多模态输入**: 支持文本、图片、文件等输入方式
⚙️ **应用管理**: 支持创建多个独立的对话应用

现在我已经连接到真正的大语言模型，可以为您提供更智能的回复！"""

    else:
        return f"我理解您说的是：「{user_message}」\n\n我现在已经连接到真正的大语言模型，可以为您提供智能的回复。如果您的应用配置了知识库，我还可以基于知识库内容为您提供更准确的信息。"

async def call_qwen_api(messages: list, model: str, temperature: float, max_tokens: int) -> str:
    """调用通义千问API"""
    import httpx

    api_key = settings_db.get('qwenApiKey')
    if not api_key:
        return "请先在设置中配置通义千问API Key。"

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    payload = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_tokens
    }

    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            result = response.json()
            return result['choices'][0]['message']['content']
    except Exception as e:
        return f"通义千问API调用失败：{str(e)}"

# API路由
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "欢迎使用 AIChat Backend API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "app_name": "AIChat Backend",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

# 认证相关API
@app.post("/api/auth/register")
async def register(user: UserCreate):
    """用户注册"""
    if user.username in users_db:
        raise HTTPException(status_code=400, detail="用户名已存在")
    
    users_db[user.username] = {
        "username": user.username,
        "email": user.email,
        "password": user.password,  # 实际应用中需要加密
        "created_at": datetime.now().isoformat()
    }
    
    return {"message": "注册成功", "username": user.username}

@app.post("/api/auth/login")
async def login(user: UserLogin):
    """用户登录"""
    if user.username not in users_db:
        raise HTTPException(status_code=401, detail="用户名或密码错误")

    stored_user = users_db[user.username]
    if stored_user["password"] != user.password:
        raise HTTPException(status_code=401, detail="用户名或密码错误")

    # 实际应用中应该返回JWT token
    return {
        "access_token": "mock_jwt_token_for_" + user.username,
        "token_type": "bearer",
        "expires_in": 86400,  # 24小时
        "user": {
            "username": stored_user["username"],
            "email": stored_user["email"]
        }
    }

@app.get("/api/auth/me")
async def get_current_user():
    """获取当前用户信息"""
    # 在实际应用中，这里应该验证JWT token并返回对应用户信息
    # 现在为了演示，直接返回admin用户信息
    return {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "full_name": "管理员",
        "created_at": "2024-01-01T00:00:00",
        "is_active": True,
        "is_admin": True
    }

@app.post("/api/auth/logout")
async def logout():
    """用户登出"""
    return {"message": "登出成功"}

# 应用管理API
@app.get("/api/apps")
async def get_apps():
    """获取应用列表"""
    return list(apps_db.values())

@app.post("/api/apps")
async def create_app(app: AppCreate):
    """创建应用"""
    app_id = len(apps_db) + 1
    new_app = {
        "id": app_id,
        "name": app.name,
        "description": app.description,
        "avatar_url": app.avatar_url,
        "system_prompt": app.system_prompt,
        "welcome_message": app.welcome_message,
        "model_config": app.llm_config,
        "is_active": True,
        "is_public": app.is_public,
        "owner_id": 1,  # 模拟用户ID
        "created_at": datetime.now().isoformat(),
        "updated_at": None
    }
    apps_db[app_id] = new_app

    return new_app

@app.delete("/api/apps/{app_id}")
async def delete_app(app_id: int):
    """删除应用"""
    if app_id not in apps_db:
        raise HTTPException(status_code=404, detail="应用不存在")

    del apps_db[app_id]
    return {"message": "应用删除成功"}

# 对话API
@app.post("/api/conversations/chat", response_model=ChatResponse)
async def chat(message_data: ChatMessage):
    """聊天接口"""
    try:
        # 验证输入数据
        if not message_data.message or message_data.message.strip() == '':
            return ChatResponse(
                response="错误：消息内容不能为空",
                conversation_id=message_data.conversation_id or "default",
                timestamp=datetime.now().isoformat()
            )

        if not message_data.app_id or message_data.app_id.strip() == '':
            return ChatResponse(
                response="错误：应用ID不能为空，请先选择一个应用",
                conversation_id=message_data.conversation_id or "default",
                timestamp=datetime.now().isoformat()
            )

        # 生成AI回复
        response = await generate_ai_response(
            message_data.message,
            message_data.conversation_id or "default",
            message_data.app_id
        )
        
        # 保存对话记录
        conversation_key = f"{message_data.conversation_id}_{message_data.app_id}"
        if conversation_key not in conversations_db:
            conversations_db[conversation_key] = []
        
        timestamp = datetime.now().isoformat()
        conversations_db[conversation_key].append({
            "message": message_data.message,
            "response": response,
            "timestamp": timestamp
        })
        
        return ChatResponse(
            response=response,
            conversation_id=message_data.conversation_id,
            timestamp=timestamp
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"聊天处理失败: {str(e)}")

@app.get("/api/conversations/{conversation_id}")
async def get_conversation(conversation_id: str, app_id: str = "default"):
    """获取对话历史"""
    conversation_key = f"{conversation_id}_{app_id}"
    if conversation_key not in conversations_db:
        return {"conversation": []}
    
    return {"conversation": conversations_db[conversation_key]}

# 文档管理API
@app.get("/api/documents")
async def get_documents():
    """获取文档列表"""
    return {"documents": []}

@app.post("/api/documents/upload")
async def upload_document():
    """文档上传"""
    return {"message": "文档上传功能暂未实现", "status": "pending"}

# 知识库API
@app.get("/api/knowledge")
async def get_knowledge_bases():
    """获取所有知识库列表"""
    return list(knowledge_bases_db.values())

@app.get("/api/knowledge/app/{app_id}")
async def get_knowledge_bases_by_app(app_id: int):
    """获取指定应用的知识库列表"""
    app_knowledge_bases = [kb for kb in knowledge_bases_db.values() if kb and isinstance(kb, dict) and kb.get('app_id') == app_id]
    return app_knowledge_bases

@app.post("/api/knowledge")
async def create_knowledge_base(kb_data: KnowledgeBaseCreate):
    """创建知识库"""
    # 检查应用是否存在
    if kb_data.app_id not in apps_db:
        raise HTTPException(status_code=404, detail="应用不存在")

    kb_id = len(knowledge_bases_db) + 1
    new_kb = {
        "id": kb_id,
        "name": kb_data.name,
        "description": kb_data.description,
        "app_id": kb_data.app_id,
        "embedding_model": kb_data.embedding_model,
        "chunk_size": kb_data.chunk_size,
        "chunk_overlap": kb_data.chunk_overlap,
        "is_active": True,
        "created_at": datetime.now().isoformat(),
        "documents": []
    }

    knowledge_bases_db[kb_id] = new_kb
    return new_kb

@app.delete("/api/knowledge/{kb_id}")
async def delete_knowledge_base(kb_id: int):
    """删除知识库"""
    if kb_id not in knowledge_bases_db:
        raise HTTPException(status_code=404, detail="知识库不存在")

    del knowledge_bases_db[kb_id]
    return {"message": "知识库删除成功"}

@app.post("/api/knowledge/{kb_id}/documents")
async def upload_document(kb_id: int, doc_data: DocumentUpload):
    """上传文档到知识库"""
    if kb_id not in knowledge_bases_db:
        raise HTTPException(status_code=404, detail="知识库不存在")

    kb = knowledge_bases_db[kb_id]
    doc_id = len(kb['documents']) + 1

    new_doc = {
        "id": doc_id,
        "name": doc_data.filename,
        "content": doc_data.content,
        "uploaded_at": datetime.now().isoformat()
    }

    kb['documents'].append(new_doc)
    return {"message": "文档上传成功", "document": new_doc}

@app.get("/api/knowledge/{kb_id}/documents")
async def get_knowledge_base_documents(kb_id: int):
    """获取知识库的文档列表"""
    if kb_id not in knowledge_bases_db:
        raise HTTPException(status_code=404, detail="知识库不存在")

    kb = knowledge_bases_db[kb_id]
    if not kb or not isinstance(kb, dict):
        raise HTTPException(status_code=500, detail="知识库数据异常")

    return kb.get('documents', [])

# 设置API
@app.get("/api/settings")
async def get_settings():
    """获取系统设置"""
    return settings_db

@app.put("/api/settings")
async def save_settings(settings: SystemSettings):
    """保存系统设置"""
    # 更新设置
    for key, value in settings.dict(exclude_unset=True).items():
        if key in settings_db:
            settings_db[key] = value

    return settings_db

@app.post("/api/settings/reset")
async def reset_settings():
    """重置设置为默认值"""
    global settings_db
    settings_db = {
        "defaultModel": "gpt-3.5-turbo",
        "temperature": 0.7,
        "maxTokens": 2000,
        "enableStreaming": True,
        "openaiApiKey": None,
        "openaiBaseUrl": "https://api.openai.com/v1",
        "ollamaBaseUrl": "http://localhost:11434",
        "maxFileSize": 50,
        "supportedFileTypes": "pdf,doc,docx,txt,md",
        "enableMonitoring": False,
        "enableLogging": True
    }
    return settings_db

if __name__ == "__main__":
    print("🚀 启动AIChat后端服务...")
    print("📖 API文档: http://localhost:8000/docs")
    print("🌐 服务地址: http://localhost:8000")
    
    uvicorn.run(
        "backend_simple:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
