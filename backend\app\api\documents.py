from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel
import os
import uuid
from datetime import datetime
import asyncio

from ..core.database import get_db
from ..core.config import settings
from ..models.document import Document
from ..models.knowledge_base import KnowledgeBase
from ..models.app import App
from ..models.user import User
from .auth import get_current_user
from ..services.document_processor import document_processor
from ..services.vector_store import vector_store_manager

router = APIRouter()


class DocumentResponse(BaseModel):
    id: int
    filename: str
    original_filename: str
    file_size: int
    file_type: str
    title: Optional[str] = None
    summary: Optional[str] = None
    status: str
    total_chunks: int
    knowledge_base_id: int
    created_at: datetime
    processed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


@router.get("/", response_model=List[DocumentResponse])
async def get_all_documents(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有文档列表"""
    documents = db.query(Document).filter(
        Document.is_active == True
    ).offset(skip).limit(limit).all()

    return documents


@router.post("/upload/{kb_id}", response_model=DocumentResponse)
async def upload_document(
    kb_id: int,
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """上传文档到知识库"""
    # 检查知识库是否存在且用户有权限
    kb = db.query(KnowledgeBase).filter(KnowledgeBase.id == kb_id).first()
    if not kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在"
        )
    
    app = db.query(App).filter(App.id == kb.app_id).first()
    if app.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    # 检查文件类型
    file_extension = file.filename.split('.')[-1].lower()
    if file_extension not in settings.ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的文件类型: {file_extension}"
        )
    
    # 检查文件大小
    file_content = await file.read()
    if len(file_content) > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="文件大小超过限制"
        )
    
    # 生成唯一文件名
    file_id = str(uuid.uuid4())
    filename = f"{file_id}.{file_extension}"
    file_path = os.path.join(settings.UPLOAD_PATH, filename)
    
    # 保存文件
    with open(file_path, "wb") as f:
        f.write(file_content)
    
    # 创建文档记录
    db_document = Document(
        filename=filename,
        original_filename=file.filename,
        file_path=file_path,
        file_size=len(file_content),
        file_type=file_extension,
        mime_type=file.content_type,
        knowledge_base_id=kb_id,
        status="pending"
    )
    
    db.add(db_document)
    db.commit()
    db.refresh(db_document)
    
    # 异步处理文档（解析、切片、向量化）
    background_tasks.add_task(process_document_async, db_document.id)

    return db_document


async def process_document_async(document_id: int):
    """异步处理文档"""
    db = next(get_db())
    try:
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document:
            return

        # 更新状态为处理中
        document.status = "processing"
        db.commit()

        try:
            # 处理文档内容
            result = document_processor.process_document(
                document.file_path,
                document.file_type
            )

            if result['success']:
                # 更新文档信息
                document.content = result['content']
                document.title = result.get('title', document.original_filename)
                document.summary = document_processor.extract_summary(result['content'])

                # 添加到向量数据库
                success = vector_store_manager.add_document_to_knowledge_base(
                    document.knowledge_base_id,
                    document.id,
                    result['content'],
                    {
                        'filename': document.original_filename,
                        'file_type': document.file_type,
                        'title': document.title,
                        **result.get('metadata', {})
                    }
                )

                if success:
                    document.status = "completed"
                    document.processed_at = datetime.utcnow()

                    # 更新知识库统计
                    kb = db.query(KnowledgeBase).filter(
                        KnowledgeBase.id == document.knowledge_base_id
                    ).first()
                    if kb:
                        kb.total_chunks = db.query(Document).filter(
                            Document.knowledge_base_id == kb.id,
                            Document.status == "completed"
                        ).count()
                        db.commit()
                else:
                    document.status = "failed"
                    document.error_message = "向量化处理失败"
            else:
                document.status = "failed"
                document.error_message = result.get('error', '文档处理失败')

        except Exception as e:
            document.status = "failed"
            document.error_message = str(e)

        db.commit()

    except Exception as e:
        print(f"异步处理文档失败: {e}")
    finally:
        db.close()


@router.get("/kb/{kb_id}", response_model=List[DocumentResponse])
async def get_documents_by_kb(
    kb_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取知识库的文档列表"""
    # 检查权限
    kb = db.query(KnowledgeBase).filter(KnowledgeBase.id == kb_id).first()
    if not kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在"
        )
    
    app = db.query(App).filter(App.id == kb.app_id).first()
    if app.owner_id != current_user.id and not app.is_public:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    documents = db.query(Document).filter(
        Document.knowledge_base_id == kb_id
    ).offset(skip).limit(limit).all()
    
    return documents


@router.delete("/{doc_id}")
async def delete_document(
    doc_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除文档"""
    document = db.query(Document).filter(Document.id == doc_id).first()
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文档不存在"
        )
    
    # 检查权限
    kb = db.query(KnowledgeBase).filter(KnowledgeBase.id == document.knowledge_base_id).first()
    app = db.query(App).filter(App.id == kb.app_id).first()
    if app.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    # 从向量数据库删除
    vector_store_manager.delete_document_from_knowledge_base(
        document.knowledge_base_id,
        document.id
    )

    # 删除文件
    if os.path.exists(document.file_path):
        os.remove(document.file_path)

    # 删除数据库记录
    db.delete(document)
    db.commit()

    return {"message": "文档删除成功"}
