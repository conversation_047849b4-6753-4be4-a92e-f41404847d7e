#!/usr/bin/env python3
"""
模拟前端认证流程测试
"""

import requests
import json

def test_frontend_auth_flow():
    """模拟前端认证流程"""
    
    print("🔍 模拟前端认证流程测试")
    
    # 1. 登录获取token
    print("\n步骤1: 登录获取token")
    login_url = "http://localhost:8000/api/auth/login"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        print(f"登录状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            token = result.get('access_token')
            print(f"✅ 登录成功!")
            print(f"Token: {token[:50]}...")
            
            # 2. 使用token访问 /api/auth/me
            print(f"\n步骤2: 使用token访问 /api/auth/me")
            me_url = "http://localhost:8000/api/auth/me"
            
            # 模拟前端axios的请求方式
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            me_response = requests.get(me_url, headers=headers)
            print(f"获取用户信息状态码: {me_response.status_code}")
            print(f"请求头: {headers}")
            
            if me_response.status_code == 200:
                user_info = me_response.json()
                print(f"✅ 获取用户信息成功!")
                print(f"用户信息: {json.dumps(user_info, indent=2, ensure_ascii=False)}")
            else:
                print(f"❌ 获取用户信息失败")
                print(f"响应内容: {me_response.text}")
                print(f"响应头: {dict(me_response.headers)}")
            
            # 3. 测试其他需要认证的接口
            print(f"\n步骤3: 测试其他需要认证的接口")
            
            # 测试获取应用列表
            apps_url = "http://localhost:8000/api/apps"
            apps_response = requests.get(apps_url, headers=headers)
            print(f"获取应用列表状态码: {apps_response.status_code}")
            
            if apps_response.status_code == 200:
                print(f"✅ 获取应用列表成功!")
            else:
                print(f"❌ 获取应用列表失败: {apps_response.text}")
            
            # 4. 测试错误的token
            print(f"\n步骤4: 测试错误的token")
            wrong_headers = {
                "Authorization": "Bearer wrong_token",
                "Content-Type": "application/json"
            }
            
            wrong_response = requests.get(me_url, headers=wrong_headers)
            print(f"错误token状态码: {wrong_response.status_code}")
            print(f"错误token响应: {wrong_response.text}")
            
            # 5. 测试没有Authorization头
            print(f"\n步骤5: 测试没有Authorization头")
            no_auth_response = requests.get(me_url)
            print(f"无认证头状态码: {no_auth_response.status_code}")
            print(f"无认证头响应: {no_auth_response.text}")
            
        else:
            print(f"❌ 登录失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_frontend_auth_flow()
