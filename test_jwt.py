#!/usr/bin/env python3
"""
测试JWT token生成和验证
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

import requests
import json
from datetime import datetime, timedelta
from jose import jwt, JW<PERSON><PERSON>r

def test_jwt_token():
    """测试JWT token生成和验证"""
    
    # 1. 先登录获取token
    print("🔍 步骤1: 登录获取JWT token")
    login_url = "http://localhost:8000/api/auth/login"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        print(f"登录状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            token = result.get('access_token')
            print(f"✅ 登录成功!")
            print(f"Token: {token}")
            
            # 2. 解析token内容
            print(f"\n🔍 步骤2: 解析JWT token内容")
            try:
                # 不验证签名，只解析内容
                payload = jwt.get_unverified_claims(token)
                print(f"Token payload: {json.dumps(payload, indent=2)}")
                
                # 检查过期时间
                exp = payload.get('exp')
                if exp:
                    exp_time = datetime.fromtimestamp(exp)
                    now = datetime.now()
                    print(f"Token过期时间: {exp_time}")
                    print(f"当前时间: {now}")
                    print(f"Token是否过期: {now > exp_time}")
                
            except Exception as e:
                print(f"❌ 解析token失败: {e}")
            
            # 3. 测试 /api/auth/me 接口
            print(f"\n🔍 步骤3: 测试 /api/auth/me 接口")
            me_url = "http://localhost:8000/api/auth/me"
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            try:
                me_response = requests.get(me_url, headers=headers)
                print(f"获取用户信息状态码: {me_response.status_code}")
                print(f"响应内容: {me_response.text}")
                
                if me_response.status_code == 200:
                    user_info = me_response.json()
                    print(f"✅ 获取用户信息成功!")
                    print(f"用户信息: {json.dumps(user_info, indent=2)}")
                else:
                    print(f"❌ 获取用户信息失败")
                    
            except Exception as e:
                print(f"❌ 请求用户信息失败: {e}")
            
            # 4. 手动验证token
            print(f"\n🔍 步骤4: 手动验证JWT token")
            try:
                from backend.app.core.config import settings
                print(f"JWT密钥: {settings.JWT_SECRET_KEY}")
                print(f"JWT算法: {settings.JWT_ALGORITHM}")
                
                # 验证token
                payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
                print(f"✅ Token验证成功!")
                print(f"验证后的payload: {json.dumps(payload, indent=2)}")
                
            except JWTError as e:
                print(f"❌ Token验证失败: {e}")
            except Exception as e:
                print(f"❌ 导入配置失败: {e}")
                
        else:
            print(f"❌ 登录失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")

if __name__ == "__main__":
    test_jwt_token()
