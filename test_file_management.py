#!/usr/bin/env python3
"""
测试文件管理功能：上传、向量化、删除、RAG
"""

import requests
import json
import time
import io

BASE_URL = "http://localhost:8000"

def login():
    """登录获取token"""
    response = requests.post(f"{BASE_URL}/api/auth/login", json={
        'username': 'admin',
        'password': 'admin123'
    })
    if response.status_code == 200:
        return response.json()['access_token']
    else:
        raise Exception(f"登录失败: {response.text}")

def test_file_management():
    """测试文件管理功能"""
    print("🚀 开始测试文件管理功能")
    
    # 1. 登录
    print("\n1. 登录获取token...")
    token = login()
    headers = {'Authorization': f'Bearer {token}'}
    print("✅ 登录成功")
    
    # 2. 获取或创建应用
    print("\n2. 获取应用列表...")
    apps_response = requests.get(f"{BASE_URL}/api/apps", headers=headers)
    apps = apps_response.json()
    if apps:
        app_id = apps[0]['id']
        print(f"✅ 使用现有应用 ID: {app_id}")
    else:
        # 创建新应用
        app_data = {
            'name': '测试应用',
            'description': '用于测试文件管理功能'
        }
        app_response = requests.post(f"{BASE_URL}/api/apps", json=app_data, headers=headers)
        app_id = app_response.json()['id']
        print(f"✅ 创建新应用 ID: {app_id}")
    
    # 3. 获取或创建知识库
    print("\n3. 获取知识库列表...")
    kb_response = requests.get(f"{BASE_URL}/api/knowledge/", headers=headers)
    knowledge_bases = kb_response.json()
    
    # 找到属于当前应用的知识库
    app_kbs = [kb for kb in knowledge_bases if kb['app_id'] == app_id]
    
    if app_kbs:
        kb_id = app_kbs[0]['id']
        print(f"✅ 使用现有知识库 ID: {kb_id}")
    else:
        # 创建新知识库
        kb_data = {
            'name': '测试知识库',
            'description': '用于测试文件管理功能',
            'app_id': app_id
        }
        kb_create_response = requests.post(f"{BASE_URL}/api/knowledge/", json=kb_data, headers=headers)
        kb_id = kb_create_response.json()['id']
        print(f"✅ 创建新知识库 ID: {kb_id}")
    
    # 4. 测试文本上传
    print("\n4. 测试文本上传...")
    text_data = {
        'filename': '测试文档.txt',
        'content': '''这是一个测试文档的内容。

本文档包含了关于人工智能和机器学习的基础知识：

1. 人工智能（AI）是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。

2. 机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。

3. 深度学习是机器学习的一个子集，使用神经网络来模拟人脑的工作方式。

4. 自然语言处理（NLP）是AI的一个领域，专注于计算机与人类语言之间的交互。

5. 计算机视觉是AI的另一个重要领域，使计算机能够理解和解释视觉信息。

这些技术正在改变我们的世界，从自动驾驶汽车到智能助手，AI无处不在。''',
        'knowledge_base_id': kb_id
    }
    
    text_response = requests.post(f"{BASE_URL}/api/knowledge/{kb_id}/documents/text", 
                                 json=text_data, headers=headers)
    if text_response.status_code == 200:
        doc_data = text_response.json()
        doc_id = doc_data['id']
        print(f"✅ 文本上传成功，文档ID: {doc_id}")
        print(f"   状态: {doc_data['status']}")
    else:
        print(f"❌ 文本上传失败: {text_response.text}")
        return
    
    # 5. 等待文档处理完成
    print("\n5. 等待文档处理完成...")
    max_wait = 30  # 最多等待30秒
    wait_time = 0
    
    while wait_time < max_wait:
        docs_response = requests.get(f"{BASE_URL}/api/knowledge/{kb_id}/documents", headers=headers)
        if docs_response.status_code == 200:
            docs = docs_response.json()
            doc = next((d for d in docs if d['id'] == doc_id), None)
            if doc:
                print(f"   当前状态: {doc['status']}")
                if doc['status'] == 'completed':
                    print("✅ 文档处理完成")
                    break
                elif doc['status'] == 'failed':
                    print(f"❌ 文档处理失败: {doc.get('error_message', '未知错误')}")
                    return
        
        time.sleep(2)
        wait_time += 2
    
    if wait_time >= max_wait:
        print("⚠️ 文档处理超时，继续测试...")
    
    # 6. 测试RAG对话
    print("\n6. 测试RAG对话...")
    chat_data = {
        'message': '什么是机器学习？',
        'app_id': str(app_id)
    }
    
    chat_response = requests.post(f"{BASE_URL}/api/conversations/chat", 
                                 json=chat_data, headers=headers)
    if chat_response.status_code == 200:
        chat_result = chat_response.json()
        print("✅ RAG对话成功")
        print(f"   回答: {chat_result['response'][:200]}...")
        print(f"   RAG启用: {chat_result.get('rag_enabled', False)}")
        print(f"   检索到的知识库: {chat_result.get('knowledge_bases_used', [])}")
        print(f"   检索到的片段数: {len(chat_result.get('retrieved_chunks', []))}")
    else:
        print(f"❌ RAG对话失败: {chat_response.text}")
    
    # 7. 测试文档删除
    print("\n7. 测试文档删除...")
    delete_response = requests.delete(f"{BASE_URL}/api/knowledge/{kb_id}/documents/{doc_id}", 
                                     headers=headers)
    if delete_response.status_code == 200:
        print("✅ 文档删除成功")
    else:
        print(f"❌ 文档删除失败: {delete_response.status_code} - {delete_response.text}")
    
    # 8. 验证删除结果
    print("\n8. 验证删除结果...")
    docs_response = requests.get(f"{BASE_URL}/api/knowledge/{kb_id}/documents", headers=headers)
    if docs_response.status_code == 200:
        docs = docs_response.json()
        remaining_docs = [d for d in docs if d['id'] == doc_id]
        if not remaining_docs:
            print("✅ 文档已成功删除")
        else:
            print("⚠️ 文档仍然存在")
    else:
        print(f"❌ 获取文档列表失败: {docs_response.text}")

if __name__ == "__main__":
    try:
        test_file_management()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
