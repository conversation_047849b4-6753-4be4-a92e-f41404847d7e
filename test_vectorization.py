#!/usr/bin/env python3
"""
测试向量化功能
"""

import requests
import json
import time
import sys
import os

# 添加后端路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

BASE_URL = "http://localhost:8000"

def test_vectorization():
    """测试向量化功能"""
    print("🔍 测试向量化功能")
    
    # 1. 登录
    print("\n1. 登录...")
    login_response = requests.post(f"{BASE_URL}/api/auth/login", json={
        'username': 'admin',
        'password': 'admin123'
    })
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.text}")
        return
    
    token = login_response.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    print("✅ 登录成功")
    
    # 2. 获取知识库
    print("\n2. 获取知识库...")
    kb_response = requests.get(f"{BASE_URL}/api/knowledge/", headers=headers)
    if kb_response.status_code != 200:
        print(f"❌ 获取知识库失败: {kb_response.text}")
        return
    
    knowledge_bases = kb_response.json()
    if not knowledge_bases:
        print("❌ 没有找到知识库")
        return
    
    kb_id = knowledge_bases[0]['id']
    print(f"✅ 使用知识库 ID: {kb_id}")
    
    # 3. 上传测试文档
    print("\n3. 上传测试文档...")
    text_data = {
        'filename': '向量化测试文档.txt',
        'content': '''这是一个用于测试向量化功能的文档。

人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。

机器学习（Machine Learning，ML）是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。机器学习算法通过训练数据来构建数学模型，以便对新数据做出预测或决策。

深度学习（Deep Learning，DL）是机器学习的一个子集，它使用多层神经网络来模拟人脑的工作方式。深度学习在图像识别、自然语言处理和语音识别等领域取得了显著成果。

自然语言处理（Natural Language Processing，NLP）是人工智能的一个重要分支，专注于计算机与人类语言之间的交互。NLP技术使计算机能够理解、解释和生成人类语言。

计算机视觉（Computer Vision，CV）是人工智能的另一个重要领域，它使计算机能够理解和解释视觉信息，如图像和视频。

这些技术正在改变我们的世界，从自动驾驶汽车到智能助手，AI技术无处不在。''',
        'knowledge_base_id': kb_id
    }
    
    upload_response = requests.post(f"{BASE_URL}/api/knowledge/{kb_id}/documents/text", 
                                   json=text_data, headers=headers)
    
    if upload_response.status_code != 200:
        print(f"❌ 文档上传失败: {upload_response.text}")
        return
    
    doc_data = upload_response.json()
    doc_id = doc_data['id']
    print(f"✅ 文档上传成功，ID: {doc_id}")
    print(f"   初始状态: {doc_data['status']}")
    
    # 4. 等待处理完成
    print("\n4. 等待文档处理...")
    max_wait = 60  # 最多等待60秒
    wait_time = 0
    
    while wait_time < max_wait:
        docs_response = requests.get(f"{BASE_URL}/api/knowledge/{kb_id}/documents", headers=headers)
        if docs_response.status_code == 200:
            docs = docs_response.json()
            doc = next((d for d in docs if d['id'] == doc_id), None)
            if doc:
                print(f"   状态: {doc['status']} (等待时间: {wait_time}s)")
                if doc['status'] == 'completed':
                    print("✅ 文档处理完成")
                    print(f"   总块数: {doc['total_chunks']}")
                    break
                elif doc['status'] == 'failed':
                    print(f"❌ 文档处理失败")
                    return
        
        time.sleep(3)
        wait_time += 3
    
    if wait_time >= max_wait:
        print("⚠️ 文档处理超时")
        return
    
    # 5. 测试RAG检索
    print("\n5. 测试RAG检索...")
    
    # 获取应用ID
    apps_response = requests.get(f"{BASE_URL}/api/apps", headers=headers)
    if apps_response.status_code != 200:
        print(f"❌ 获取应用失败: {apps_response.text}")
        return
    
    apps = apps_response.json()
    if not apps:
        print("❌ 没有找到应用")
        return
    
    app_id = apps[0]['id']
    
    # 测试不同的问题
    test_questions = [
        "什么是人工智能？",
        "机器学习和深度学习有什么区别？",
        "自然语言处理是什么？",
        "计算机视觉的应用有哪些？"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n   问题 {i}: {question}")
        
        chat_data = {
            'message': question,
            'app_id': str(app_id)
        }
        
        chat_response = requests.post(f"{BASE_URL}/api/conversations/chat", 
                                     json=chat_data, headers=headers)
        
        if chat_response.status_code == 200:
            result = chat_response.json()
            print(f"   ✅ RAG启用: {result.get('rag_enabled', False)}")
            print(f"   📚 使用知识库: {result.get('knowledge_bases_used', [])}")
            print(f"   📄 检索片段数: {len(result.get('retrieved_chunks', []))}")
            print(f"   🤖 回答: {result['response'][:150]}...")
            
            if result.get('rag_enabled') and result.get('retrieved_chunks'):
                print("   ✅ RAG功能正常工作")
            else:
                print("   ⚠️ RAG功能可能未正常工作")
        else:
            print(f"   ❌ 对话失败: {chat_response.text}")
    
    # 6. 测试文档删除
    print(f"\n6. 测试文档删除 (ID: {doc_id})...")
    delete_response = requests.delete(f"{BASE_URL}/api/knowledge/{kb_id}/documents/{doc_id}", 
                                     headers=headers)
    
    if delete_response.status_code == 200:
        print("✅ 文档删除成功")
        
        # 验证删除
        docs_response = requests.get(f"{BASE_URL}/api/knowledge/{kb_id}/documents", headers=headers)
        if docs_response.status_code == 200:
            docs = docs_response.json()
            remaining_docs = [d for d in docs if d['id'] == doc_id]
            if not remaining_docs:
                print("✅ 文档已从数据库中删除")
            else:
                print("⚠️ 文档仍在数据库中")
        
    else:
        print(f"❌ 文档删除失败: {delete_response.status_code} - {delete_response.text}")

if __name__ == "__main__":
    try:
        test_vectorization()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
