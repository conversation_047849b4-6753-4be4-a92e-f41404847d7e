#!/usr/bin/env python3
"""
API路由测试脚本
检查所有API接口是否正常工作
"""

import requests
import json
import sys
from datetime import datetime

# API基础URL
BASE_URL = "http://localhost:8000"

# 测试用户凭据
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

def test_api_endpoint(method, endpoint, data=None, headers=None, description=""):
    """测试API端点"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=headers, timeout=10)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, headers=headers, timeout=10)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers, timeout=10)
        else:
            print(f"❌ 不支持的HTTP方法: {method}")
            return False
            
        status_code = response.status_code
        
        if status_code == 200:
            print(f"✅ {method} {endpoint} - {description} (状态码: {status_code})")
            return True
        elif status_code == 401:
            print(f"🔐 {method} {endpoint} - {description} (需要认证: {status_code})")
            return True  # 401是预期的，说明接口存在
        elif status_code == 403:
            print(f"🚫 {method} {endpoint} - {description} (权限不足: {status_code})")
            return True  # 403是预期的，说明接口存在
        elif status_code == 404:
            print(f"❌ {method} {endpoint} - {description} (接口不存在: {status_code})")
            return False
        elif status_code == 405:
            print(f"❌ {method} {endpoint} - {description} (方法不允许: {status_code})")
            return False
        elif status_code == 422:
            print(f"⚠️  {method} {endpoint} - {description} (参数错误: {status_code})")
            return True  # 422说明接口存在但参数有问题
        else:
            print(f"⚠️  {method} {endpoint} - {description} (状态码: {status_code})")
            return True
            
    except requests.exceptions.ConnectionError:
        print(f"❌ {method} {endpoint} - {description} (连接失败)")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ {method} {endpoint} - {description} (请求超时)")
        return False
    except Exception as e:
        print(f"❌ {method} {endpoint} - {description} (错误: {str(e)})")
        return False

def get_auth_token():
    """获取认证token"""
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/login",
            json=TEST_USER,
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            return data.get("access_token")
        else:
            print(f"⚠️  登录失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"⚠️  登录异常: {str(e)}")
        return None

def main():
    """主测试函数"""
    print("🚀 开始测试API接口...")
    print(f"📍 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 API基础URL: {BASE_URL}")
    print("=" * 60)
    
    # 获取认证token
    print("\n🔐 获取认证token...")
    token = get_auth_token()
    headers = {}
    if token:
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ 认证token获取成功")
    else:
        print("⚠️  认证token获取失败，将测试未认证的接口")
    
    # 测试基础接口
    print("\n📋 测试基础接口...")
    test_api_endpoint("GET", "/", description="根路径")
    test_api_endpoint("GET", "/api/health", description="健康检查")
    
    # 测试认证接口
    print("\n🔐 测试认证接口...")
    test_api_endpoint("POST", "/api/auth/register", {"username": "test", "email": "<EMAIL>", "password": "test123"}, description="用户注册")
    test_api_endpoint("POST", "/api/auth/login", TEST_USER, description="用户登录")
    test_api_endpoint("GET", "/api/auth/me", headers=headers, description="获取当前用户")
    test_api_endpoint("POST", "/api/auth/logout", headers=headers, description="用户登出")
    
    # 测试用户管理接口
    print("\n👥 测试用户管理接口...")
    test_api_endpoint("GET", "/api/users", headers=headers, description="获取用户列表")
    test_api_endpoint("GET", "/api/users/1", headers=headers, description="获取用户详情")
    
    # 测试应用管理接口
    print("\n📱 测试应用管理接口...")
    test_api_endpoint("GET", "/api/apps", headers=headers, description="获取应用列表")
    test_api_endpoint("POST", "/api/apps", {"name": "测试应用", "description": "测试"}, headers=headers, description="创建应用")
    test_api_endpoint("GET", "/api/apps/1", headers=headers, description="获取应用详情")
    
    # 测试知识库接口
    print("\n📚 测试知识库接口...")
    test_api_endpoint("GET", "/api/knowledge", headers=headers, description="获取知识库列表")
    test_api_endpoint("POST", "/api/knowledge", {"name": "测试知识库", "app_id": 1}, headers=headers, description="创建知识库")
    test_api_endpoint("GET", "/api/knowledge/app/1", headers=headers, description="获取应用的知识库")
    
    # 测试文档管理接口
    print("\n📄 测试文档管理接口...")
    test_api_endpoint("GET", "/api/documents", headers=headers, description="获取文档列表")
    test_api_endpoint("GET", "/api/documents/kb/1", headers=headers, description="获取知识库文档")
    
    # 测试对话接口
    print("\n💬 测试对话接口...")
    test_api_endpoint("GET", "/api/conversations", headers=headers, description="获取对话列表")
    test_api_endpoint("POST", "/api/conversations", {"app_id": 1, "title": "测试对话"}, headers=headers, description="创建对话")
    test_api_endpoint("POST", "/api/conversations/chat", {"message": "你好", "app_id": "1"}, headers=headers, description="通用聊天")
    
    # 测试设置接口
    print("\n⚙️  测试设置接口...")
    test_api_endpoint("GET", "/api/settings", headers=headers, description="获取系统设置")
    test_api_endpoint("PUT", "/api/settings", {"defaultModel": "gpt-3.5-turbo"}, headers=headers, description="更新系统设置")
    test_api_endpoint("POST", "/api/settings/reset", headers=headers, description="重置系统设置")
    test_api_endpoint("GET", "/api/settings/models", description="获取可用模型")
    
    print("\n" + "=" * 60)
    print("✅ API接口测试完成!")

if __name__ == "__main__":
    main()
