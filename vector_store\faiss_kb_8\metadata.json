{"0": {"id": "doc_9_chunk_0", "text": "这是一个用于直接处理测试的文档。\n\n人工智能（AI）是一门研究如何让计算机模拟人类智能的科学。它包括以下几个主要领域：\n\n1. 机器学习（Machine Learning）\n   - 监督学习：使用标记数据训练模型\n   - 无监督学习：从未标记数据中发现模式\n   - 强化学习：通过试错学习最优策略\n\n2. 深度学习（Deep Learning）\n   - 神经网络：模拟人脑神经元的计算模型\n   - 卷积神经网络（CNN）：主要用于图像处理\n   - 循环神经网络（RNN）：主要用于序列数据处理\n\n3. 自然语言处理（NLP）\n   - 文本分析：理解和处理人类语言\n   - 语言生成：自动生成人类可读的文本\n   - 机器翻译：在不同语言之间进行翻译\n\n4. 计算机视觉（Computer Vision）\n   - 图像识别：识别图像中的对象和场景\n   - 目标检测：定位图像中的特定对象\n   - 图像分割：将图像分割成不同的区域\n\n这些技术正在改变我们的生活方式，从智能手机到自动驾驶汽车，AI技术无处不在。", "metadata": {"document_id": 9, "knowledge_base_id": 8, "chunk_index": 0, "filename": "直接处理测试文档.txt", "file_type": "txt", "title": "直接处理测试文档.txt"}}, "1": {"id": "doc_10_chunk_0", "text": "这是一个用于直接处理测试的文档。\n\n人工智能（AI）是一门研究如何让计算机模拟人类智能的科学。它包括以下几个主要领域：\n\n1. 机器学习（Machine Learning）\n   - 监督学习：使用标记数据训练模型\n   - 无监督学习：从未标记数据中发现模式\n   - 强化学习：通过试错学习最优策略\n\n2. 深度学习（Deep Learning）\n   - 神经网络：模拟人脑神经元的计算模型\n   - 卷积神经网络（CNN）：主要用于图像处理\n   - 循环神经网络（RNN）：主要用于序列数据处理\n\n3. 自然语言处理（NLP）\n   - 文本分析：理解和处理人类语言\n   - 语言生成：自动生成人类可读的文本\n   - 机器翻译：在不同语言之间进行翻译\n\n4. 计算机视觉（Computer Vision）\n   - 图像识别：识别图像中的对象和场景\n   - 目标检测：定位图像中的特定对象\n   - 图像分割：将图像分割成不同的区域\n\n这些技术正在改变我们的生活方式，从智能手机到自动驾驶汽车，AI技术无处不在。", "metadata": {"document_id": 10, "knowledge_base_id": 8, "chunk_index": 0, "filename": "直接处理测试文档.txt", "file_type": "txt", "title": "直接处理测试文档.txt"}}, "2": {"id": "doc_11_chunk_0", "text": "这是一个用于直接处理测试的文档。\n\n人工智能（AI）是一门研究如何让计算机模拟人类智能的科学。它包括以下几个主要领域：\n\n1. 机器学习（Machine Learning）\n   - 监督学习：使用标记数据训练模型\n   - 无监督学习：从未标记数据中发现模式\n   - 强化学习：通过试错学习最优策略\n\n2. 深度学习（Deep Learning）\n   - 神经网络：模拟人脑神经元的计算模型\n   - 卷积神经网络（CNN）：主要用于图像处理\n   - 循环神经网络（RNN）：主要用于序列数据处理\n\n3. 自然语言处理（NLP）\n   - 文本分析：理解和处理人类语言\n   - 语言生成：自动生成人类可读的文本\n   - 机器翻译：在不同语言之间进行翻译\n\n4. 计算机视觉（Computer Vision）\n   - 图像识别：识别图像中的对象和场景\n   - 目标检测：定位图像中的特定对象\n   - 图像分割：将图像分割成不同的区域\n\n这些技术正在改变我们的生活方式，从智能手机到自动驾驶汽车，AI技术无处不在。", "metadata": {"document_id": 11, "knowledge_base_id": 8, "chunk_index": 0, "filename": "直接处理测试文档.txt", "file_type": "txt", "title": "直接处理测试文档.txt"}}, "3": {"id": "doc_12_chunk_0", "text": "这是一个用于直接处理测试的文档。\n\n人工智能（AI）是一门研究如何让计算机模拟人类智能的科学。它包括以下几个主要领域：\n\n1. 机器学习（Machine Learning）\n   - 监督学习：使用标记数据训练模型\n   - 无监督学习：从未标记数据中发现模式\n   - 强化学习：通过试错学习最优策略\n\n2. 深度学习（Deep Learning）\n   - 神经网络：模拟人脑神经元的计算模型\n   - 卷积神经网络（CNN）：主要用于图像处理\n   - 循环神经网络（RNN）：主要用于序列数据处理\n\n3. 自然语言处理（NLP）\n   - 文本分析：理解和处理人类语言\n   - 语言生成：自动生成人类可读的文本\n   - 机器翻译：在不同语言之间进行翻译\n\n4. 计算机视觉（Computer Vision）\n   - 图像识别：识别图像中的对象和场景\n   - 目标检测：定位图像中的特定对象\n   - 图像分割：将图像分割成不同的区域\n\n这些技术正在改变我们的生活方式，从智能手机到自动驾驶汽车，AI技术无处不在。", "metadata": {"document_id": 12, "knowledge_base_id": 8, "chunk_index": 0, "filename": "直接处理测试文档.txt", "file_type": "txt", "title": "直接处理测试文档.txt"}}, "4": {"id": "doc_13_chunk_0", "text": "这是一个用于直接处理测试的文档。\n\n人工智能（AI）是一门研究如何让计算机模拟人类智能的科学。它包括以下几个主要领域：\n\n1. 机器学习（Machine Learning）\n   - 监督学习：使用标记数据训练模型\n   - 无监督学习：从未标记数据中发现模式\n   - 强化学习：通过试错学习最优策略\n\n2. 深度学习（Deep Learning）\n   - 神经网络：模拟人脑神经元的计算模型\n   - 卷积神经网络（CNN）：主要用于图像处理\n   - 循环神经网络（RNN）：主要用于序列数据处理\n\n3. 自然语言处理（NLP）\n   - 文本分析：理解和处理人类语言\n   - 语言生成：自动生成人类可读的文本\n   - 机器翻译：在不同语言之间进行翻译\n\n4. 计算机视觉（Computer Vision）\n   - 图像识别：识别图像中的对象和场景\n   - 目标检测：定位图像中的特定对象\n   - 图像分割：将图像分割成不同的区域\n\n这些技术正在改变我们的生活方式，从智能手机到自动驾驶汽车，AI技术无处不在。", "metadata": {"document_id": 13, "knowledge_base_id": 8, "chunk_index": 0, "filename": "直接处理测试文档.txt", "file_type": "txt", "title": "直接处理测试文档.txt"}}, "5": {"id": "doc_14_chunk_0", "text": "这是一个用于直接处理测试的文档。\n\n人工智能（AI）是一门研究如何让计算机模拟人类智能的科学。它包括以下几个主要领域：\n\n1. 机器学习（Machine Learning）\n   - 监督学习：使用标记数据训练模型\n   - 无监督学习：从未标记数据中发现模式\n   - 强化学习：通过试错学习最优策略\n\n2. 深度学习（Deep Learning）\n   - 神经网络：模拟人脑神经元的计算模型\n   - 卷积神经网络（CNN）：主要用于图像处理\n   - 循环神经网络（RNN）：主要用于序列数据处理\n\n3. 自然语言处理（NLP）\n   - 文本分析：理解和处理人类语言\n   - 语言生成：自动生成人类可读的文本\n   - 机器翻译：在不同语言之间进行翻译\n\n4. 计算机视觉（Computer Vision）\n   - 图像识别：识别图像中的对象和场景\n   - 目标检测：定位图像中的特定对象\n   - 图像分割：将图像分割成不同的区域\n\n这些技术正在改变我们的生活方式，从智能手机到自动驾驶汽车，AI技术无处不在。", "metadata": {"document_id": 14, "knowledge_base_id": 8, "chunk_index": 0, "filename": "直接处理测试文档.txt", "file_type": "txt", "title": "直接处理测试文档.txt"}}}