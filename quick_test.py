#!/usr/bin/env python3
import requests
import json

# 1. 登录
login_response = requests.post("http://localhost:8000/api/auth/login", json={"username": "admin", "password": "admin123"})
print(f"登录状态: {login_response.status_code}")

if login_response.status_code == 200:
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    
    # 2. 获取应用列表
    apps_response = requests.get("http://localhost:8000/api/apps", headers=headers)
    print(f"获取应用列表状态: {apps_response.status_code}")
    if apps_response.status_code == 200:
        apps = apps_response.json()
        print(f"现有应用数量: {len(apps)}")
        for app in apps:
            print(f"  - {app['name']} (ID: {app['id']})")
    
    # 3. 创建新应用
    app_data = {
        "name": "快速测试应用",
        "description": "快速测试",
        "system_prompt": "你是一个测试助手",
        "welcome_message": "欢迎！",
        "llm_config": {"model": "gpt-3.5-turbo"},
        "is_public": False
    }
    
    create_response = requests.post("http://localhost:8000/api/apps", json=app_data, headers=headers)
    print(f"创建应用状态: {create_response.status_code}")
    if create_response.status_code == 200:
        new_app = create_response.json()
        print(f"✅ 创建成功! 应用ID: {new_app['id']}, 名称: {new_app['name']}")
    else:
        print(f"❌ 创建失败: {create_response.text}")
else:
    print(f"❌ 登录失败: {login_response.text}")
