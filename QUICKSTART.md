# AIChat 快速启动指南

## 🚀 5分钟快速体验

### 前置要求
- Docker 和 Docker Compose
- 8GB+ 可用内存
- 10GB+ 可用磁盘空间

### 快速启动

1. **克隆项目**
```bash
git clone <your-repo-url>
cd AIChat
```

2. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件（可选，使用默认配置也能运行）
# nano .env
```

3. **一键启动**
```bash
# 使用 Makefile（推荐）
make init

# 或者使用 Docker Compose
docker-compose up -d
```

4. **等待服务启动**
```bash
# 查看启动状态
make status

# 查看日志
make logs
```

5. **访问应用**
- 🌐 前端界面: http://localhost:3000
- 🔧 后端API: http://localhost:8000
- 📚 API文档: http://localhost:8000/docs

### 默认登录信息
- 用户名: `admin`
- 密码: `admin123`

## 📝 基本使用流程

### 1. 登录系统
使用默认管理员账户登录，或注册新账户。

### 2. 创建应用
1. 进入"应用管理"页面
2. 点击"创建应用"
3. 填写应用信息：
   - 应用名称：如"智能客服"
   - 描述：应用的用途说明
   - 系统提示词：定义AI的角色和行为
   - 欢迎消息：用户进入对话时的欢迎语

### 3. 创建知识库
1. 在应用详情页面创建知识库
2. 配置知识库参数：
   - 名称和描述
   - 文档分块大小（默认1000字符）
   - 分块重叠（默认200字符）

### 4. 上传文档
1. 进入知识库管理页面
2. 上传支持的文档格式：
   - 📄 PDF文件
   - 📝 Word文档（DOC/DOCX）
   - 📊 Excel表格（XLS/XLSX）
   - 🎯 PowerPoint演示文稿（PPT/PPTX）
   - 📋 文本文件（TXT/MD）
   - 🖼️ 图片文件（JPG/PNG/GIF）

### 5. 开始对话
1. 选择已配置的应用
2. 开始智能对话
3. 系统会自动检索相关知识库内容
4. 获得基于知识库的准确回答

## 🔧 常用命令

```bash
# 查看服务状态
make status

# 查看所有日志
make logs

# 查看特定服务日志
make logs-backend
make logs-frontend

# 重启服务
make restart

# 停止服务
make down

# 清理所有数据（谨慎使用）
make clean

# 备份数据
make backup

# 运行测试
make test

# 代码格式化
make format
```

## 🛠️ 配置大语言模型

### OpenAI GPT（推荐）
```bash
# 在 .env 文件中配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo
```

### 本地 Ollama
```bash
# 启动 Ollama 服务
docker-compose up ollama -d

# 下载模型（在容器内执行）
docker exec aichat_ollama ollama pull llama3

# 配置使用 Ollama
OLLAMA_MODEL=llama3
```

### 通义千问
```bash
# 配置 API Key
QWEN_API_KEY=your_qwen_api_key_here
```

## 📊 监控和管理

### 查看系统状态
```bash
# 查看容器状态
docker ps

# 查看资源使用
docker stats

# 查看磁盘使用
df -h
```

### 数据库管理
```bash
# 进入数据库
make shell-postgres

# 备份数据库
make backup

# 查看数据库大小
docker exec aichat_postgres psql -U aichat_user -d aichat -c "SELECT pg_size_pretty(pg_database_size('aichat'));"
```

```bash
# 启动命令
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```



## 🚨 故障排除

### 服务启动失败
```bash
# 检查端口占用
netstat -tulpn | grep :8000
netstat -tulpn | grep :3000

# 检查 Docker 状态
docker system df
docker system prune -f
```

### 内存不足
```bash
# 检查内存使用
free -h

# 调整 Docker 内存限制
# 编辑 docker-compose.yml 中的 memory 配置
```

### 数据库连接问题
```bash
# 检查数据库状态
docker exec aichat_postgres pg_isready -U aichat_user

# 重启数据库
docker restart aichat_postgres
```

### 向量数据库问题
```bash
# 清理向量数据库
rm -rf vector_store/*

# 重新处理文档
# 在管理界面重新上传文档
```

## 🎯 下一步

1. **配置生产环境**
   - 参考 `docker-compose.prod.yml`
   - 配置 HTTPS 和域名
   - 设置监控和备份

2. **自定义配置**
   - 调整模型参数
   - 配置文件上传限制
   - 设置用户权限

3. **扩展功能**
   - 集成更多大模型
   - 添加自定义插件
   - 开发 API 集成

## 📞 获取帮助

- 📖 查看完整文档: [README.md](README.md)
- 🐛 报告问题: 提交 GitHub Issue
- 💬 技术讨论: 加入社区群组
- 📧 联系我们: [<EMAIL>]

---

🎉 **恭喜！** 您已成功启动 AIChat 智能对话系统！

现在可以开始创建您的第一个智能应用了。
