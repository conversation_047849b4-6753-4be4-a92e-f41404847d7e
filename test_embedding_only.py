#!/usr/bin/env python3
"""
仅测试向量化功能
"""

import sys
import os

# 添加后端路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.vector_store import vector_store_manager

def test_embedding_only():
    """仅测试向量化功能"""
    print("🔧 测试向量化功能")
    
    # 测试文本
    test_text = """这是一个测试文档。

人工智能（AI）是一门研究如何让计算机模拟人类智能的科学。它包括以下几个主要领域：

1. 机器学习（Machine Learning）
   - 监督学习：使用标记数据训练模型
   - 无监督学习：从未标记数据中发现模式
   - 强化学习：通过试错学习最优策略

2. 深度学习（Deep Learning）
   - 神经网络：模拟人脑神经元的计算模型
   - 卷积神经网络（CNN）：主要用于图像处理
   - 循环神经网络（RNN）：主要用于序列数据处理

这些技术正在改变我们的生活方式。"""
    
    try:
        # 1. 测试文本分割
        print("\n1. 测试文本分割...")
        chunks = vector_store_manager.create_text_chunks(test_text, chunk_size=200, chunk_overlap=50)
        print(f"   ✅ 分割成 {len(chunks)} 个片段")
        for i, chunk in enumerate(chunks):
            print(f"   片段 {i+1}: {chunk[:50]}...")
        
        # 2. 测试向量存储初始化
        print("\n2. 测试向量存储初始化...")
        knowledge_base_id = 8
        vector_store = vector_store_manager.get_vector_store(knowledge_base_id)
        print(f"   ✅ 向量存储初始化成功: {type(vector_store).__name__}")
        
        # 3. 测试embedding生成
        print("\n3. 测试embedding生成...")
        if hasattr(vector_store, '_get_embeddings'):
            embeddings = vector_store._get_embeddings(chunks[:2])  # 只测试前2个片段
            print(f"   ✅ 生成embeddings成功，形状: {embeddings.shape}")
            print(f"   向量维度: {embeddings.shape[1]}")
        else:
            print("   ❌ 向量存储没有_get_embeddings方法")
        
        # 4. 测试添加文档（不保存到数据库）
        print("\n4. 测试添加文档到向量存储...")
        chunk_ids = [f"test_{i}" for i in range(len(chunks))]
        chunk_metadatas = [{"chunk_index": i, "source": "test"} for i in range(len(chunks))]
        
        success = vector_store.add_documents(chunks, chunk_metadatas, chunk_ids)
        if success:
            print("   ✅ 文档添加到向量存储成功")
        else:
            print("   ❌ 文档添加到向量存储失败")
        
        # 5. 测试搜索
        print("\n5. 测试向量搜索...")
        query = "什么是机器学习？"
        results = vector_store.search(query, k=3)
        print(f"   ✅ 搜索返回 {len(results)} 个结果")
        for i, result in enumerate(results):
            print(f"   结果 {i+1}: {result.get('text', '')[:50]}... (相似度: {result.get('similarity_score', 0):.3f})")
        
        # 6. 测试删除文档
        print("\n6. 测试删除文档...")
        delete_success = vector_store.delete_documents(chunk_ids)
        if delete_success:
            print("   ✅ 文档删除成功")
        else:
            print("   ❌ 文档删除失败")
        
        print("\n🎉 向量化功能测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_embedding_only()
