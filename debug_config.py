#!/usr/bin/env python3
"""
调试配置信息
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def debug_config():
    """调试配置信息"""
    
    print("🔍 调试配置信息")
    
    try:
        from backend.app.core.config import settings
        
        print(f"APP_NAME: {settings.APP_NAME}")
        print(f"APP_VERSION: {settings.APP_VERSION}")
        print(f"DEBUG: {settings.DEBUG}")
        print(f"DATABASE_URL: {settings.DATABASE_URL}")
        print(f"JWT_SECRET_KEY: {settings.JWT_SECRET_KEY}")
        print(f"JWT_ALGORITHM: {settings.JWT_ALGORITHM}")
        print(f"JWT_EXPIRE_MINUTES: {settings.JWT_EXPIRE_MINUTES}")
        
        # 检查环境变量
        print(f"\n🔍 环境变量:")
        print(f"JWT_SECRET_KEY (env): {os.getenv('JWT_SECRET_KEY')}")
        print(f"JWT_ALGORITHM (env): {os.getenv('JWT_ALGORITHM')}")
        print(f"JWT_EXPIRE_MINUTES (env): {os.getenv('JWT_EXPIRE_MINUTES')}")
        
        # 测试JWT生成
        print(f"\n🔍 测试JWT生成:")
        from backend.app.core.security import create_access_token
        from datetime import timedelta
        
        test_token = create_access_token(
            data={"sub": "test_user"}, 
            expires_delta=timedelta(minutes=30)
        )
        print(f"测试token: {test_token}")
        
        # 测试JWT验证
        print(f"\n🔍 测试JWT验证:")
        from backend.app.core.security import verify_token
        try:
            payload = verify_token(test_token)
            print(f"✅ 验证成功: {payload}")
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            
    except Exception as e:
        print(f"❌ 导入配置失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_config()
